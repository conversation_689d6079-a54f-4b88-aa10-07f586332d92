<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { message, Tooltip } from 'ant-design-vue';
  import { RightOutlined } from '@ant-design/icons-vue';
  import { getLocalItem } from '@/utils/common';
  import AvatarIcon from '@/assets/image/base/pictures/avatar.png';
  import EmptyPerson from '@/assets/image/base/pictures/empty_person.png';
  import AVATAR01 from '@/assets/image/avatar/2d.png';
  import AVATAR02 from '@/assets/image/avatar/3d.png';

  import { Loading } from '@/components';
  import { getPretrainList } from '@/api/avatarChat';
  import CreateDigitalHumanModal from '../Create/index.vue';

  interface PretrainItemProps {
    avatar_id: string;
    image_url: string;
    name: string;
    pretrain_status?: number;
    error_msg?: string;
  }

  // Props
  defineProps({
    isCollapsed: Boolean,
    config: Object,
    setConfig: Function,
  });

  const defaultCharacterLibrary = [
    {
      id: 0,
      created_at: '2025-03-27T14:40:39.184567+08:00',
      updated_at: '2025-03-27T14:46:07.849233+08:00',
      deleted_at: null,
      is_activated: true,
      image_url: '@/assets/image/avatar/2d.png',
      video_url: '',
      user_id: 490189310428057600,
      pretrain_status: 1,
      preload_status: 2,
      avatar_id: '5rRekX4u0M',
      source_image_url: '',
      name: '职业女性',
      gender: 'woman',
      error_msg: null,
    },
    // {
    //   id: 1,
    //   created_at: '2025-03-27T10:03:44.068392+08:00',
    //   updated_at: '2025-03-27T10:03:49.288088+08:00',
    //   deleted_at: null,
    //   is_activated: true,
    //   image_url: ' @/assets/image/avatar/3d.png',
    //   video_url: '',
    //   user_id: 490189310428057600,
    //   pretrain_status: 0,
    //   preload_status: 0,
    //   avatar_id: '5Dnw5AKqkj',
    //   source_image_url: '',
    //   name: '职业女性',
    //   gender: 'man',
    //   error_msg: null,
    // },
  ]; // Reactive state
  const activeTab = ref('1'); // 当前激活的 Tab
  const characterLibrary = ref(defaultCharacterLibrary); // 人物库数据
  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');
  const pretrainList = ref<PretrainItemProps[]>([]); // 预训练列表
  const selectedIndex = ref<number | null>(0); // 当前选中的索引
  const createModalRef = ref(); // 模态框引用

  // 获取预训练列表
  const getList = async () => {
    try {
      const data = await getPretrainList({
        user_id: userId,
      });
      pretrainList.value = data.list || [];
    } catch (error) {
      console.log('获取列表失败', error);
      message.error('获取列表失败');
    }
  };

  // 切换 Tab
  const handleTabChange = (key: string) => {
    activeTab.value = key;
    getList();
  };

  // 打开模态框
  const handleOpenModal = () => {
    console.log('打开模态框', createModalRef.value);
    createModalRef.value?.openModal();
  };

  // 关闭模态框
  const handleCloseModal = () => {
    activeTab.value = '2';
    getList();
  };

  // 获取预训练列表时调用
  onMounted(() => {
    getList();
  });
</script>

<template>
  <div class="rolesContainer" :style="{ overflow: isCollapsed ? 'hidden' : '324px' }">
    <!-- <div class="createRole">
      <a-image class="avatar" :src="AvatarIcon" :preview="false" />
      <div class="createRole_text" @click="handleOpenModal">
        <span>创建数字人</span>
        <RightOutlined />
      </div>
    </div> -->
    <div class="tab-buttons">
      <div class="tab-button" :class="{ active: activeTab === '1' }" @click="() => handleTabChange('1')">人物库</div>
      <div class="tab-button" :class="{ active: activeTab === '2' }" @click="() => handleTabChange('2')">我的人物</div>
    </div>
    <div class="hidden_sider_container">
      <template v-if="activeTab === '1'">
        <div v-for="(item, index) in characterLibrary" :key="index" class="imageBox">
          <img v-if="index === 0" class="img" :src="AVATAR01" :preview="false" alt="静态图片" />
          <img v-if="index === 1" class="img" :src="AVATAR02" :preview="false" alt="静态图片" />
          <Tooltip placement="bottom" title="职场女性">
            <span class="roleName">{{ item.name }}</span>
          </Tooltip>
        </div>
      </template>
      <template v-else>
        <div v-if="pretrainList.length === 0" class="noData">
          <a-image :src="EmptyPerson" :preview="false" />
          <p class="noData_text">这里还没有人物，可点击创建~</p>
          <a-button type="primary" ghost @click="handleOpenModal">创建人物</a-button>
        </div>
        <template v-else>
          <div class="createRole">
            <!-- <a-image class="avatar" :src="AvatarIcon" :preview="false" /> -->
            <div class="createRole_text" @click="handleOpenModal">
              <span style="width: 20px; height: 20px; font-size: 20px; font-weight: 600"> + </span>
              <span>创建数字人</span>
              <!-- <RightOutlined /> -->
            </div>
          </div>
          <div
            v-for="(item, index) in pretrainList"
            :key="item.avatar_id"
            class="imageBox"
            :class="{ selected: selectedIndex === index }"
          >
            <img class="img" :src="item.image_url" alt="" :preview="false" />
            <Tooltip placement="bottom" :title="item.name">
              <span class="roleName">{{ item.name }}</span>
            </Tooltip>
            <Loading v-if="item.pretrain_status === 0" :state="item.error_msg || '训练失败'" />
            <Loading v-else-if="item.pretrain_status === 2" state="训练中..." />
          </div>
        </template>
      </template>
    </div>
    <CreateDigitalHumanModal ref="createModalRef" :handle-close-modal="handleCloseModal" />
  </div>
</template>

<style lang="less" scoped>
  .rolesContainer {
    width: 100%;
    height: auto;

    .hidden_sider_container {
      display: flex;
      justify-content: flex-start;
      border-radius: 8px;
      flex-wrap: wrap;
      max-height: 685px; /* 设置固定高度 */
      overflow-y: auto; /* 启用纵向滚动 */
      overflow-x: hidden; /* 禁用横向滚动 */
      padding-left: 10px;

      .createRole {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 280px;
        height: 40px;
        margin: 5px 25px 5px 15px;
        border-radius: 4px;
        border: 1px dashed #dce0e6;
        z-index: 10;

        &:hover {
          border: 1px solid #1777ff;
        }

        :deep(.ant-image-img) {
          width: 36px;
          height: 36px;
          margin: 12px 0 12px 20px;
        }

        .createRole_text {
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          span {
            font-size: 16px;
            // width: 70px;
            height: 20px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #636466;
            line-height: 20px;
            text-align: left;
            font-style: normal;
          }
        }
      }

      ::-webkit-scrollbar {
        display: none; /* 隐藏滚动条 */
      }

      -ms-overflow-style: none; /* 适用于 IE 和 Edge */
      scrollbar-width: none; /* 适用于 Firefox */

      .noData {
        display: flex;
        justify-items: center;
        flex-direction: column;
        align-items: center;
        margin: 0 auto; /* 确保内容在父容器中居中 */
        text-align: center;
        text-align: center;
      }

      .imageBox {
        width: 132px;
        high: 176px;
        cursor: pointer;
        overflow: hidden;
        position: relative;
        border-radius: 8px;
        margin: 10px;
        background: linear-gradient(135deg, #fafafa 0%, #e6e6e6 100%);
        border: 2px solid transparent;

        &:hover {
          border: 1px solid #1777ff;
        }

        &.selected {
          border: 2px solid #1777ff;
        }

        .img {
          padding-left: 20px;
          width: 108px;
          height: 161px;
          object-fit: contain;
        }

        .roleName {
          position: absolute;
          bottom: -2px;
          width: 140px;
          height: 28px;
          background: rgba(226, 233, 242, 0.6);
          border-radius: 0px 0px 4px 4px;
          z-index: 10;
          display: flex;
          justify-content: center;
          align-items: center;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          // margin-left: 6px;
        }
      }
    }

    .tab-buttons {
      display: flex;
      // justify-content: space-between;
      // width: 284px;
      height: 32px;
      margin: 10px 20px;
      border-radius: 8px;
      // background: #f2f2f2;
    }

    .tab-button {
      display: flex;
      // width: 140px;
      height: 32px;
      text-align: center;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      color: #939599;
      margin: 2px;
      padding: 6px 16px;
      border-radius: 8px;

      &:not(:last-child) {
        border-right: none;
      }
      &.active {
        background: #f2f8ff;
        color: #000000;
      }
      &:hover {
        color: #000000;
      }
    }
  }
</style>
