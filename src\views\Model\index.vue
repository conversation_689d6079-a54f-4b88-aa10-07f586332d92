<script setup lang="ts">
  import { fetchModelListV2 } from '@/api/model';
  import { onBeforeMount, reactive, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { CustomForm } from '@/components';
  import { modelFunStatus } from '@/utils/enum';
  import { fetchAllLabel } from '@/api/label';
  import type { IFormItem } from '@/interface/index';
  import type { Dayjs } from 'dayjs';
  import dayjs from 'dayjs';
  import type { IModelFormState } from '@/interface/model';

  const route = useRoute();
  const router = useRouter();
  interface IStateChildren {
    name: string;
    id: string;
  }
  interface IState {
    model: IModelFormState[];
    search: string;
    published_at: Dayjs[] | string[] | null;
    page_index: number;
    page_size: number;
    total: number;
    spinning: boolean;
    tagList: { name: string; children: IStateChildren[] }[];
    activeTags: string[];
    activeOperations: string[];
  }
  const state = reactive<IState>({
    model: [],
    search: '',
    published_at: null,
    page_index: 1,
    page_size: 9,
    total: 0,
    spinning: false,
    tagList: [],
    activeTags: [],
    activeOperations: [],
  });

  const formConfig: IFormItem[] = [
    {
      field: 'search',
      type: 'input',
      label: '模型名',
      placeholder: '请输入',
    },
    {
      field: 'published_at',
      type: 'dateRange',
      label: '发布时间',
      placeholder: ['请选择开始时间', '请选择结束时间'],
    },
  ];

  const clickInfo = (n: { id: string }) => {
    router.push({
      path: '/model/detail',
      query: { ...route.query, modelid: n.id },
    });
  };
  const jumpToTrain = (record: IModelFormState, type: string = 'train') => {
    const { id, category } = record;
    let path: string = '/text/detail';
    switch (category) {
      case 'gotocr':
      case 'ocr':
      case 'paddleocr':
        path = '/ocr/detail';
        break;
      default:
        break;
    }
    if (type === 'experience') {
      router.push({
        path,
        query: { modelId: id },
      });
    } else {
      router.push({
        path: '/model/detail',
        query: { ...route.query, modelid: id, type },
      });
    }
  };

  watch(
    [
      () => state.search,
      () => state.published_at,
      () => state.page_index,
      () => state.page_size,
      () => state.activeTags,
      () => state.activeOperations,
    ],
    async ([search, published_at, page_index, page_size], []) => {
      let times: string[] | null = null;
      if (published_at && published_at.length) {
        const startTime = dayjs(published_at[0]).format('YYYY-MM-DD') + ' 00:00:00';
        const endTime = dayjs(published_at[1]).format('YYYY-MM-DD') + ' 23:59:59';
        // 装换成ISO格式
        times = [dayjs(startTime).format('YYYY-MM-DDTHH:mm:ss'), dayjs(endTime).format('YYYY-MM-DDTHH:mm:ss')];
      }
      state.spinning = true;
      const { items, total } = await fetchModelListV2({
        page: page_index,
        limit: page_size,
        name: search || '',
        published_at: times,
        tags: state.activeTags,
        operations: state.activeOperations,
      });
      state.model = items;
      state.total = total;
      state.spinning = false;
    },
    { deep: true, immediate: true },
  );

  // @ts-expect-error
  const onFinish = ({ search, published_at }) => {
    state.search = search;
    state.published_at = published_at;
    state.activeOperations = [];
    state.activeTags = [];
  };

  // @ts-expect-error
  const clickTag = (n, oper?) => {
    if (oper) {
      if (state.activeOperations.includes(n))
        state.activeOperations.splice(
          state.activeOperations.findIndex((e) => e == n),
          1,
        );
      else state.activeOperations.push(n);
    } else {
      if (state.activeTags.includes(n.id))
        state.activeTags.splice(
          state.activeTags.findIndex((e) => e == n.id),
          1,
        );
      else state.activeTags.push(n.id);
    }
  };

  onBeforeMount(async () => {
    state.tagList = await fetchAllLabel();
  });
</script>

<template>
  <div class="market">
    <div class="left overflow-scroll">
      <div v-for="(n, i) in state.tagList" :key="i">
        <div>{{ n.name }}</div>
        <div>
          <div v-for="(t, l) in n.children.filter((e) => e.name)" :key="l" @click="clickTag(t)">
            <a-tooltip v-if="t.name.length > 18" :title="t.name">
              <a-tag :color="state.activeTags.includes(t.id) ? '#108ee9' : ''" class="ellipsis">{{ t.name }}</a-tag>
            </a-tooltip>
            <a-tag v-else :color="state.activeTags.includes(t.id) ? '#108ee9' : ''">{{ t.name }}</a-tag>
          </div>
        </div>
        <a-divider />
      </div>
      <div>
        <div>支持操作</div>
        <div>
          <div v-for="(t, l) in Object.keys(modelFunStatus)" :key="l" @click="clickTag(t, 'oper')">
            <a-tag :color="state.activeOperations.includes(t) ? '#108ee9' : ''">{{ modelFunStatus[t].label }}</a-tag>
          </div>
        </div>
        <a-divider />
      </div>
    </div>
    <div class="right">
      <a-layout-header>
        <CustomForm
          :form-items="formConfig"
          @on-finish="onFinish"
          @on-rest="onFinish({ search: '', published_at: '' })"
        />
      </a-layout-header>
      <a-spin :spinning="state.spinning">
        <a-layout-content>
          <div class="model-list overflow-scroll">
            <div v-for="(n, i) in state.model" :key="i" @click="clickInfo(n)">
              <div>{{ n.name }}</div>
              <div class="h-22px">
                <a-tag v-for="(t, ti) in n.tags.filter((e) => e.name)" :key="ti" :bordered="false" color="cyan">{{
                  t.name
                }}</a-tag>
              </div>
              <div>
                {{ n.description }}
              </div>
              <div class="footer flex">
                <div class="published_at">发布时间：{{ dayjs(n.published_at).format('YYYY-MM-DD') }}</div>
                <div class="btn-box" @click.stop>
                  <a-button v-if="n.ops.includes('deploy')" @click="jumpToTrain(n, 'deploy')">部署</a-button>
                  <a-button v-if="n.ops.includes('train')" @click="jumpToTrain(n, 'train')">训练</a-button>
                  <a-button v-if="n.ops.includes('experience')" type="primary" @click="jumpToTrain(n, 'experience')"
                    >体验</a-button
                  >
                </div>
              </div>
            </div>
          </div>
          <a-empty v-if="!state.model.length && !state.spinning" style="margin-top: -35%" />
        </a-layout-content>
      </a-spin>
      <div class="flex justify-end w-100% mt-10px">
        <a-pagination
          v-if="state.model.length"
          v-model:current="state.page_index"
          v-model:page-size="state.page_size"
          :total="state.total"
          :show-size-changer="false"
          :show-total="(total: number) => `共 ${total} 条数据`"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .ant-layout-header,
  .ant-layout-content,
  .ant-layout-footer,
  .ant-layout-sider {
    background: none !important;
  }

  .market {
    display: flex;
    height: 100%;
    overflow: hidden;
    background: #fff;

    .left {
      width: 15%;
      height: 96%;
      padding: 10px;
      margin: 20px 20px 20px 0;
      overflow: scroll;

      > div {
        > div:nth-child(1) {
          margin-bottom: 20px;
          font-weight: 600;
          color: rgb(0 0 0 / 88%);
          text-align: start;
        }

        > div:nth-child(2) {
          display: flex;
          flex-wrap: wrap;

          > div {
            .ellipsis {
              max-width: 220px;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            > span {
              cursor: pointer;
            }
          }

          > div:not(div:nth-last-child(1)) {
            margin-bottom: 15px;
          }
        }
      }
    }

    .right {
      display: flex;
      flex: 1;
      flex-direction: column;
      height: 100%;

      > .ant-layout-header {
        display: flex;
        height: 32px;
        padding: 0;
      }

      > .ant-layout-content {
        flex: 1;
      }

      .model-list {
        display: flex;
        flex-wrap: wrap;
        align-content: start;
        width: 100%;
        height: 100%;
        margin-top: 20px;
        overflow: scroll;

        > div {
          width: calc((100% - 40px) / 3);
          max-height: 230px;
          padding: 10px;
          margin: 0 10px 20px;
          cursor: pointer;
          background-color: #fff;
          border: 1px solid #e5e5e5;
          border-radius: 2px;

          > div {
            overflow: hidden;
            text-overflow: ellipsis;
          }

          > div:not(div:nth-last-child(1)) {
            margin-bottom: 10px;
          }

          > div:nth-child(1) {
            padding: 0 20px 0 0;
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            color: rgb(51 51 51 / 100%);
            letter-spacing: 0;
          }

          > div:nth-child(2) {
            display: flex;
          }

          > div:nth-child(3) {
            width: 100%;
            height: 60px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3; /* 限制显示的行数 */
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 12px;
            font-weight: 400;
            line-height: 20px;
            color: grey;
            letter-spacing: 0;
          }
          .footer {
            display: flex;
            justify-content: space-between;
            .published_at {
              height: 32px;
              display: flex;
              align-items: center;
            }
            .btn-box {
              float: right;
              display: flex;
              height: 32px;

              > button {
                display: none;
                margin-left: 20px;
              }
            }
          }
        }

        > div:hover {
          box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.1);

          button {
            display: block !important;
          }
        }

        > div:nth-child(3n + 1) {
          margin-left: 0;
        }

        > div:nth-child(3n) {
          margin-right: 0;
        }
      }

      > div:nth-child(1) {
        height: 40px;
        padding-left: 0;
        text-align: right;
      }

      :deep(.ant-spin-nested-loading) {
        height: 100%;
      }
    }
  }

  .ant-input-affix-wrapper {
    border-radius: 0;
  }

  :deep(.ant-spin-nested-loading) {
    height: calc(100% - 32px - 80px);
    overflow: hidden;

    > div {
      height: 100%;

      > main {
        height: 100%;
      }
    }
  }
</style>
