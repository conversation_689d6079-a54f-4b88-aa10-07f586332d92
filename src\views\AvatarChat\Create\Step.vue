<script setup lang="ts">
  import { defineComponent, computed } from 'vue';
  import { InfoCircleOutlined } from '@ant-design/icons-vue';

  defineProps({
    step: {
      type: Number,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    tooltips: {
      type: Array as () => string[],
      default: () => [],
    },
  });
</script>
<template>
  <div class="step-box">
    <span class="step-index">{{ step }}</span>
    <span class="step-title mr5">{{ title }}</span>
    <a-tooltip v-if="tooltips?.length" placement="right" overlay-class-name="custom-tooltip">
      <template #title>
        <div v-for="text in tooltips" :key="text" class="tip-item">
          {{ text }}
        </div>
      </template>
      <InfoCircleOutlined class="info-icon" />
    </a-tooltip>
  </div>
</template>
<style lang="less" scoped>
  .step-box {
    margin-top: 20px;
    margin-bottom: 12px;

    .step-index {
      display: inline-block;
      width: 20px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      background: #d9e9ff;
      border-radius: 20px;
    }

    .step-title {
      display: inline-block;
      height: 20px;
      margin-left: 4px;
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      color: #17181a;

      &.mr5 {
        margin-right: 5px;
      }
    }

    .info-icon {
      // position: relative;
      // top: 1px;
      font-size: 13px;
      color: #969799;
    }
  }
</style>
