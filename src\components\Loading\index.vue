<script setup lang="ts">
  import { SyncOutlined } from '@ant-design/icons-vue';

  const props = defineProps({
    state: {
      type: [String, Object],
      default: '',
    },
  });
</script>

<template>
  <div class="loading">
    <template v-if="!props.state">
      <span v-if="typeof state === 'string'" class="text">{{ props.state }}</span>
      <span v-else>{{ props.state }}</span>
    </template>
    <template v-else>
      <SyncOutlined class="sync-icon" spin />
    </template>
  </div>
</template>

<style lang="less" scoped>
  .loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    color: #fff;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    .text {
      font-size: 16px;
    }
    .sync-icon {
      width: 25px;
    }
  }
</style>
