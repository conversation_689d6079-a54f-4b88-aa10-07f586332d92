<script setup lang="ts">
  import type { SelectProps, UploadProps } from 'ant-design-vue';
  import { message } from 'ant-design-vue';
  import { DoubleRightOutlined, SwapOutlined, InboxOutlined } from '@ant-design/icons-vue';
  import { ref, reactive, onMounted } from 'vue';
  import type { IOCRByResponse, IServiceItem } from '@/interface/exploration';
  import { useRoute } from 'vue-router';
  import { OCRByUrl, fetchServiceListByModelId, uploadImages } from '@/api/exploration';
  import Service from '@/views/Exploration/Components/service.vue';
  import type { IAceOptions } from '@/interface';
  import { VAceEditor } from 'vue3-ace-editor';
  import 'ace-builds/src-noconflict/theme-chrome';
  import 'ace-builds/src-noconflict/theme-monokai.js';
  import 'ace-builds/src-noconflict/mode-python.js';
  import 'ace-builds/src-noconflict/mode-sh.js';
  import type { ColumnType } from 'ant-design-vue/es/table';
  const route = useRoute();
  interface IState {
    switchover: boolean;
    loading: boolean;
    tableLoading: boolean;
    responseKey: string;
    activeKey: number | string;
    linkUrl: string;
    response: string;
    tableResponse: Record<string, string>[];
    prompt: string;
    selectModel: string;
    editor: unknown;
    langEnum: string[];
  }
  const responseColumn: ColumnType[] = reactive([
    { title: 'key', dataIndex: 'key' },
    { title: 'value', dataIndex: 'value' },
  ]);
  const state = reactive<IState>({
    switchover: false,
    responseKey: 'default',
    activeKey: 1,
    loading: false,
    tableLoading: false,
    linkUrl: '',
    // linkUrl: '',
    response: '',
    tableResponse: [],
    prompt: 'general',
    selectModel: '',
    langEnum: ['SQL', 'Python', 'sh'],
    editor: null,
  });
  const spinning = ref(false);
  const serviceRef = ref();
  const radioStyle = reactive({
    display: 'flex',
    height: '30px',
    lineHeight: '30px',
  });
  const currentService = reactive<IServiceItem>({
    id: '',
    service_name: '',
  });
  // @ts-expect-error
  const editorInit = (editor) => (state.editor = editor);
  const aceOptions: IAceOptions = {
    enableBasicAutocompletion: true,
    enableSnippets: true,
    enableLiveAutocompletion: true,
    tabSize: 4,
    enableEmmet: true,
    fontSize: 15,
    showPrintMargin: false, // 去除编辑器里的竖线
    highlightActiveLine: true,
    useWorker: true,
    readOnly: true,
    wrap: true,
  };
  const option = ref<SelectProps['options']>([
    {
      value: 'general',
      label: '通用文字识别',
    },
    {
      value: 'identity_card_front',
      label: '身份证识别',
    },
    {
      value: 'business_license',
      label: '营业执照识别',
    },
    {
      value: 'bank_receipt',
      label: '银行水单识别',
    },
  ]);
  const handleChangePrompt = (e: string) => {
    state.response = '';
    state.tableResponse = [];
    state.tableLoading = false;
    state.prompt = e;
  };
  const fetchServiveListReq = async () => {
    const { modelId, serviceId, service_name } = route.query;
    if (modelId || serviceId) {
      // 有模型id 根据模型获取预设服务，默认选中第一个
      if (modelId) {
        spinning.value = true;
        const data: IServiceItem[] = await fetchServiceListByModelId(route.query.modelId as string);
        if (data && data.length) {
          Object.assign(currentService, data[0]);
        }
        spinning.value = false;
      } else {
        // 由模型部署页面点击去体验进去 没有模型id

        Object.assign(currentService, { id: serviceId, service_name });
      }
    }
  };
  const uploadProps = {
    // @ts-expect-error
    beforeUpload: (file: UploadProps['fileList'][number]) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
      if (!isJpgOrPng) {
        message.warn('上传的图片格式不支持！');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('上传的图片太大！');
        return false;
      }
      return isJpgOrPng && isLt10M;
    },
    customRequest: async (detail: { file: File }) => {
      const file = detail.file;
      const formData = new FormData();
      formData.append('files', file);
      state.loading = true;
      const res: { url: string }[] = await uploadImages(formData);
      state.linkUrl = res[0].url;
      state.loading = false;
    },
    multiple: false,
    fileList: [],
    accept: 'image/png,image/jpg,image/jpeg',
    showUploadList: false,
  };

  const handleCheck = async () => {
    const imageRegex = /\.(jpg|jpeg|png|gif|bmp)$/i;
    if (!imageRegex.test(state.linkUrl)) {
      return message.warn('请输入有效的图片URL');
    }
    state.tableLoading = true;
    const res: IOCRByResponse | { result: string } = await OCRByUrl({
      ocr_category: state.prompt,
      deploy_id: currentService.id,
      url: state.linkUrl.replace(/\s/g, ''),
    });
    if (state.prompt === 'general') {
      state.response = (res as { result: string }).result as string;
    } else {
      state.response = (res as IOCRByResponse).choices[0].message.content
        ? (res as IOCRByResponse).choices[0].message.content
        : '';
      state.tableResponse = state.response
        ? Object.keys(JSON.parse(state.response)).map((key) => {
            return {
              key,
              value: JSON.parse(state.response)[key],
            };
          })
        : [];
      state.tableLoading = false;
    }
  };
  const handleChangeService = (data: { service: IServiceItem }) => {
    const { service } = data;
    Object.assign(currentService, service);
    state.switchover = false;
    serviceRef.value.clearState();
  };
  onMounted(() => {
    fetchServiveListReq();
  });
</script>

<template>
  <a-spin :spinning="spinning">
    <div class="flex flex-col h-100%">
      <div class="flex flex-justify-end h-32px">
        <a-select v-model:value="state.prompt" style="width: 200px" @change="handleChangePrompt">
          <a-select-option v-for="opt in option" :key="opt.value" :value="opt.value">{{ opt.label }}</a-select-option>
        </a-select>
      </div>
      <div class="flex flex-justify-center m-t-10px">
        <div class="flex w-70% flex-justify-between h-32px line-height-32px bg-#f5f5f5 p-x-10px rounded-lg">
          {{ currentService.service_name }}
          <div class="h-32px flex flex-justify-end">
            <SwapOutlined @click="state.switchover = true" />
          </div>
        </div>
      </div>
      <div class="center flex flex-justify-center flex-1">
        <div class="w-70% flex flex-justify-around">
          <div class="left">
            <!-- <a-image v-if="state.linkUrl" :src="state.linkUrl"></a-image>
          <div v-else style="display: flex; align-items: center; justify-content: center; height: 100%">
            <a-empty description="暂无图片 "></a-empty>
          </div> -->
            <a-spin :spinning="state.loading">
              <a-upload class="avatar-uploader" v-bind="uploadProps" list-type="picture-card">
                <div v-if="state.linkUrl" class="img-box" :style="{ backgroundImage: `url(${state.linkUrl})` }" />
                <div v-else>
                  <inbox-outlined style="font-size: 40px; color: #1890ff"></inbox-outlined>
                  <div class="ant-upload-text">点击或拖动文件上传</div>
                </div>
              </a-upload>
            </a-spin>
          </div>
          <DoubleRightOutlined />
          <div class="right">
            <a-radio-group v-model:value="state.responseKey">
              <a-radio-button value="default">识别结果</a-radio-button>
              <a-radio-button value="json">JSON 返回</a-radio-button>
            </a-radio-group>
            <div v-if="state.responseKey === 'default'" class="response h-100% w-100%">
              <div v-if="state.prompt === 'general'" style="height: 100%">
                <div v-if="state.response">{{ state.response }}</div>
                <div v-else style="height: 100%; display: flex; justify-content: center">
                  <a-empty style="height: 100%; display: flex; flex-direction: column; justify-content: center" />
                </div>
              </div>
              <a-table
                v-else
                :columns="responseColumn"
                :data-source="state.tableResponse"
                :loading="state.tableLoading"
                :scroll="{ y: 480 }"
                bordered
                :pagination="false"
              ></a-table>
            </div>
            <v-ace-editor
              v-else
              ref="aceEditor"
              v-model:value="state.response"
              @init="editorInit"
              :lang="state.langEnum[2].toLowerCase()"
              theme="monokai"
              :options="aceOptions"
              :style="{ height: '550px', border: '1px solid #dbd3d3' }"
            />
            <!-- <a-tabs v-model:activeKey="activeKey" type="card" style="height: calc(100% - 40px)">
            <a-tab-pane key="default" tab="全文还原">
            </a-tab-pane>
            <a-tab-pane key="json" tab="JSON 返回">Content of Tab Pane 2</a-tab-pane>
          </a-tabs> -->
          </div>
        </div>
      </div>
      <div class="footer flex flex-justify-center h-32px">
        <div class="w-70% flex flex-justify-center">
          <a-input v-model:value="state.linkUrl" placeholder="请输入图片 URL"></a-input>
          <!-- <a-upload v-model:file-list="fileList" name="file" action="" @change="handleChange">
          <a-button> 上传图片 </a-button>
        </a-upload> -->
          <a-button type="primary" :disabled="!state.linkUrl || !currentService.id" @click="handleCheck">
            检测
          </a-button>
        </div>
      </div>
    </div>
  </a-spin>
  <Service
    ref="serviceRef"
    :ids="[currentService.id]"
    :visible="state.switchover"
    type="change"
    @cancel="state.switchover = false"
    @ok="handleChangeService"
  ></Service>
</template>

<style scoped lang="less">
  .center {
    .left {
      width: calc(50% - 15px);
      height: calc(100% - 20px);
      margin: 10px 0;
    }

    .right {
      width: calc(50% - 15px);
      height: calc(100% - 20px);
      padding: 20px;
      margin: 10px 0;
      border: 1px solid #ccc;
      border-radius: 8px;
    }

    .right {
      .response {
        height: calc(100% - 20px);
        padding-top: 10px;
      }
    }
  }
  :deep(.ant-upload-wrapper) {
    height: 100%;
    // background-color: red;
    > div {
      width: 100% !important;
      height: 100% !important;
    }
  }

  .img-box {
    width: 100%; /* 宽度自适应 */
    height: 100%; /* 高度自适应 */
    // background-image: url('your-image.jpg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
</style>
