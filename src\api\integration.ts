import request from '@/utils/request';
import { getEnv } from '@/utils';

const { VITE_APP_BLENDER_URL, VITE_APP_P2L_URL } = getEnv();

const BLENDER = VITE_APP_BLENDER_URL;
const P2L = VITE_APP_P2L_URL;

// 答案融合
export function executeBlend(data: { contents: string[]; top_K: number; models: string[]; fusion_model: '' }) {
  return request.$Axios.post(`/blend[${BLENDER}]`, data);
}

// p2l
export function executeP2L(data: { prompt: string; model_list: string[] }) {
  return request.$Axios.post(`/start[${P2L}]`, { api_key: '0330', ...data });
}

// p2l router 训练
export function routerDrill(data: any) {
  return request.$Axios.post(`/train[${P2L}]`, data);
}

// p2l router 查询训练状态
export function drillStatus(task_id: any) {
  return request.$Axios.get(`/status/${task_id}[${P2L}]`);
}
