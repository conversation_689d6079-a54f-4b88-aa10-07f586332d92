<script lang="ts" setup>
  import { ref } from 'vue';
  import VideoGeneration from './VideoGeneration/index.vue';
  // import StyleTransfer from './styleTransfer/index.vue';

  const activeKey = ref('1');
</script>

<template>
  <a-tabs v-model:active-key="activeKey">
    <a-tab-pane key="1" tab="图文生视频">
      <VideoGeneration v-if="activeKey === '1'" />
    </a-tab-pane>
    <a-tab-pane key="2" tab="视频编辑" disabled force-render>视频编辑</a-tab-pane>
  </a-tabs>
</template>
