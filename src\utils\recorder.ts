/* eslint-disable @typescript-eslint/no-explicit-any */
import Recorder from 'recorder-core';
import 'recorder-core/src/engine/wav.js';
// 实时转码并上传-通用版
let rec: any = null;
// 是否是录音开始
let isStart: any = true;
// blob文件转换成base64
const blobToBase64 = (blob: any) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(blob);
    reader.onload = () => {
      const base64 = reader.result;
      resolve(base64);
    };
    reader.onerror = (error) => reject(error);
  });
};

let testSampleRate = 16000;
let testBitRate = 60;

//重置环境，每次开始录音时必须先调用此方法，清理环境
let RealTimeSendTryReset = function (type = 'wav') {
  realTimeSendTryType = type;
  realTimeSendTryTime = 0;
};
let realTimeSendTryType: any;
let realTimeSendTryEncBusy: any;
let realTimeSendTryTime: any = 0;
let realTimeSendTryNumber: any;
let transferUploadNumberMax: any;
let realTimeSendTryChunk: any;

//============ 实时处理核心函数，处理和转换音频数据，并将其上传 =================
let RealTimeSendTry = function (
  buffers: any,
  bufferSampleRate: any,
  isClose: any,
  send?: any,
  _SendInterval?: any,
  _isStart?: any,
) {
  let t1 = Date.now();

  //表示是一次新的录音或处理周期的开始
  if (realTimeSendTryTime === 0) {
    realTimeSendTryTime = t1; //将当前时间戳赋值给realTimeSendTryTime，用作后续操作的时间基准
    realTimeSendTryEncBusy = 0; //编码忙碌标志
    realTimeSendTryNumber = 0; //处理的音频块数
    transferUploadNumberMax = 0; //最大上传次数
    realTimeSendTryChunk = null;
  }

  // 分段传输0.3s ,isClose false
  if (!isClose && t1 - realTimeSendTryTime < 300) {
    return; //控制缓冲达到指定间隔才进行传输
  }

  const BufferTime = Date.now();
  realTimeSendTryTime = t1;
  let number = ++realTimeSendTryNumber;

  //Pulse Code Modulation,音频数据格式，它直接表示模拟音频信号的振幅,非压缩的、原始的音频数据
  let pcm = [],
    pcmSampleRate = 0;
  if (buffers.length > 0) {
    //借用SampleData函数进行数据的连续处理，采样率转换是顺带的，得到新的pcm数据
    // chunk对象通常包括转换后的PCM数据数组、新的采样率、以及其他可能的元数据，用于后续的音频处理或上传操作
    let chunk = Recorder.SampleData(buffers, bufferSampleRate, testSampleRate, realTimeSendTryChunk, {
      frameType: isClose ? '' : realTimeSendTryType,
    });
    // FIXED
    //清理已处理完的缓冲数据，释放内存以支持长时间录音，最后完成录音时不能调用stop，因为数据已经被清掉了
    // for (
    //   var i = realTimeSendTryChunk ? realTimeSendTryChunk.index : 0;
    //   i < chunk.index;
    //   i++
    // ) {
    //   buffers[i] = null;
    // }
    realTimeSendTryChunk = chunk;

    pcm = chunk.data;
    pcmSampleRate = chunk.sampleRate;
  }

  //没有新数据，或结束时的数据量太小，不能进行mock转码
  if (pcm.length === 0 || (isClose && pcm.length < 2000)) {
    console.error('pcm结束', isClose);
    TransferUpload(number, null, 0, null, isClose, send, 1000);
    return;
  }

  //实时编码队列阻塞处理
  if (!isClose) {
    if (realTimeSendTryEncBusy >= 2) {
      console.log('编码队列阻塞，已丢弃一帧', 1);
      return;
    }
  }
  realTimeSendTryEncBusy++;

  let encStartTime = Date.now();
  let recMock = Recorder({
    type: 'wav',
    sampleRate: testSampleRate, //采样率
    bitRate: testBitRate, //比特率
  });
  recMock.mock(pcm, pcmSampleRate);
  recMock.stop(
    function (blob: any, duration: number) {
      realTimeSendTryEncBusy && realTimeSendTryEncBusy--;
      blob.encTime = Date.now() - encStartTime;
      //转码好就推入传输
      TransferUpload(
        number,
        blob,
        duration,
        recMock,
        isClose,
        send,
        BufferTime,
        // isStart
      );
    },
    function (msg: any) {
      realTimeSendTryEncBusy && realTimeSendTryEncBusy--;
      console.log('转码错误:' + msg, 1);
    },
  );
};
//=====数据传输函数==========
let TransferUpload = function (
  number: any,
  blobOrNull: any,
  _duration: any,
  _blobRec: any,
  isClose: any,
  send: any,
  _SendInterval: any,
  // isStart?:any
) {
  transferUploadNumberMax = Math.max(transferUploadNumberMax, number);
  if (blobOrNull) {
    let blob = blobOrNull;
    //*********发送方式一：Base64文本发送***************
    let reader = new FileReader();
    reader.onloadend = function () {
      let base64 = (/.+;\s*base64\s*,\s*(.+)$/i.exec(reader.result as string) || [])[1];
      send &&
        send({
          wav_base64: base64,
          isLast: false,
          isStart: !!isStart,
        });
      if (isStart) {
        isStart = false;
      }
    };
    reader.readAsDataURL(blob);
  }
  if (isClose) {
    console.log('结束传输了', isClose);
  }
};
/**调用open打开录音请求好录音权限**/
const recOpen = function (
  success: () => void, //成功打开录音，用于处理录音开始后的逻辑
  fail: (res: any) => void, //打开录音失败或者用户拒绝授权时调用的回调函数
  send: (val: any) => void, //将录音数据发送出去的函数，用于处理和发送实时录音数据
  onProcess?: (val: any) => void,
) {
  console.log('用户打开录音');
  isStart = true;
  //音频录制对象 Recorder，设置为WAV的音频格式，用户允许录音才能进行录音，遵守用户隐私跟安全
  rec = Recorder({
    type: 'wav',
    sampleRate: 16000, //采样率
    bitRate: 60, //mp3格式，指定采样率hz、比特率kbps，其他参数使用默认配置；注意：是数字的参数必须提供数字，不要用字符串；需要使用的type类型，需提前把格式支持文件加载进来，比如使用wav格式需要提前加载wav.js编码引擎
    onProcess: function (
      buffers: any, //音频数据缓冲区
      powerLevel: any, //声音强度级别
      _bufferDuration: any, //缓冲区持续时间
      bufferSampleRate: any, //数据的采样率
      _newBufferIdx: any, //新的缓冲区索引
      _asyncEnd: any, //一个标志，指示异步处理是否结束
    ) {
      onProcess && onProcess({ powerLevel });
      //负责处理和传输实时音频数据
      RealTimeSendTry(buffers, bufferSampleRate, false, send, 1000, isStart);
      // RealTimeSendTry(buffers, bufferSampleRate, false, send, 5000);
    },
  });
  rec.open(
    function (_msg: any, _isUserNotAllow: any) {
      //打开麦克风授权获得相关资源
      success && success();
      RealTimeSendTryReset(); //重置环境，开始录音时必须调用一次
    },
    function (_msg: any, _isUserNotAllow: any) {
      fail && fail('用户拒绝了录音权限,请检查录音设备！');
      //用户拒绝未授权或不支持
      //dialog&&dialog.Cancel(); 如果开启了弹框，此处需要取消
    },
  );
};
/**开始录音**/
function recStart() {
  //打开了录音后才能进行start、stop调用
  rec.start();
}
/**结束录音**/
function recStop(send: (val: any) => void) {
  rec?.stop(
    function (blob: any, _duration: any) {
      rec.close(); //释放录音资源，当然可以不释放，后面可以连续调用start；但不释放时系统或浏览器会一直提示在录音，最佳操作是录完就close掉
      rec = null;
      //拿到blob文件对象：立即播放、上传、下载保存
      blobToBase64(blob).then((_res: any) => {
        send &&
          send({
            wav_base64: '',
            isLast: true,
          });
      });
    },
    function (msg: any) {
      rec.close();
      rec = null;
      console.log('录音失败:' + msg);
    },
  );
}
export { blobToBase64, recOpen, recStart, recStop };
