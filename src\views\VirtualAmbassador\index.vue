<script lang="ts" setup>
  import { ref } from 'vue';
  import { Modal } from 'ant-design-vue';
  import Generation from './generation/index.vue';
  import Work from './work/index.vue';

  const activeKey = ref('1');
  const generationRef = ref();

  const handleTabChange = (key: string) => {
    if (key === '2' && generationRef.value?.hasUnsaved()) {
      activeKey.value = '1';
      Modal.confirm({
        title: '确定离开当前页面吗？',
        okText: '确定离开',
        cancelText: '取消',
        onOk: () => {
          activeKey.value = '2';
        },
        onCancel: () => {
          activeKey.value = '1';
        },
      });
    } else {
      activeKey.value = key;
    }
  };
</script>

<template>
  <a-tabs v-model:active-key="activeKey" @change="handleTabChange">
    <a-tab-pane key="1" tab="一键生成">
      <Generation v-if="activeKey === '1'" ref="generationRef" />
    </a-tab-pane>
    <a-tab-pane key="2" tab="我的作品"><Work v-if="activeKey === '2'" /></a-tab-pane>
  </a-tabs>
</template>
