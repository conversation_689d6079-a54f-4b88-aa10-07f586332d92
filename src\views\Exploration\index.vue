<script setup lang="ts">
  import { fetchOCRModelList, fetchTextChartModelList } from '@/api/exploration';
  import { ref, reactive, onMounted, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import type { IModelListResponse } from '@/interface/exploration';
  const route = useRoute();
  const router = useRouter();
  const modelList: IModelListResponse[] = reactive([]);
  const spinning = ref(false);

  const getModelList = async () => {
    let data: IModelListResponse[] = [];
    spinning.value = true;
    try {
      if (route.path === '/text') {
        data = await fetchTextChartModelList();
      } else if (route.path === '/ocr') {
        data = await fetchOCRModelList();
      }
    } catch {}
    spinning.value = false;
    modelList.length = 0;
    modelList.push(...data);
  };

  const handleJump = (id: string) => {
    router.push({
      path: `${route.path}/detail`,
      query: { modelId: id },
    });
    console.log(route.path, 'route.path');
  };

  watch(
    () => route,
    () => {
      getModelList();
    },
    { deep: true, immediate: true },
  );
  onMounted(() => {
    getModelList();
  });
</script>

<template>
  <template v-if="route.query.modelId || route.query.serviceId">
    <router-view></router-view>
  </template>
  <div v-else class="h-100%">
    <a-spin :spinning="spinning" tip="模型准备中，敬请期待... ">
      <div v-if="modelList.length" class="wrapper">
        <h1>选择模型，立即开始体验</h1>
        <div class="container">
          <div v-for="item in modelList" :key="item.id" class="item" @click="handleJump(item.id)">
            <h3>{{ item.name }}</h3>
            <!-- <a-tooltip :title="item.description">
            <p>{{ item.description || '暂无简介' }}</p>
          </a-tooltip> -->
            <!-- modelFunStatus -->
            <div class="h-22px m-b-10px">
              <a-tag v-for="t in item.tags" :key="t" :bordered="false" color="cyan">{{ t.name }}</a-tag>
            </div>
            <p>
              {{ item.description || '暂无简介' }}
            </p>
          </div>
        </div>
      </div>
      <div v-else style="display: flex; align-items: center; justify-content: center; height: 100%">
        <a-empty description="模型准备中，敬请期待... "></a-empty>
      </div>
    </a-spin>
  </div>
</template>

<style scoped lang="less">
  .wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    padding-top: 10%;

    > h1 {
      margin-bottom: 30px;
      font-size: 30px;
    }

    .container {
      display: flex;
      justify-content: center;
      width: 80%;
      height: 170px;

      .item {
        display: flex;
        flex-direction: column;
        // justify-content: center;
        width: 30%;
        height: 100%;
        padding: 16px 16px 10px;
        cursor: pointer;
        border: 1px solid #ccc;
        border-radius: 6px;
        margin-right: 20px;
        &:last-child {
          margin-right: 0;
        }

        > h3 {
          font-size: 24px;
        }

        > p {
          display: -webkit-box;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 3; /* 设置显示的行数 */
          line-height: 20px;
          word-wrap: break-word;
          -webkit-box-orient: vertical;
          color: #808080;
        }

        &:hover {
          border-color: #1890ff;
        }
      }
    }
  }

  // :deep(.ant-tooltip) {
  //   :deep(.ant-tooltip-inner) {
  //     width: 1000px !important;
  //   }
  //   // overflow: hidden;
  //   // text-overflow: ellipsis;
  // }
</style>
