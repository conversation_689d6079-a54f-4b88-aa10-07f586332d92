.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: normal;
}

.create-digital-man-modal {
  display: grid;
  grid-template-columns: 1fr 1fr; /* 两列等宽 */
  font-family: PingFangSC, 'PingFang SC', sans-serif;
  cursor: default;
  border-top: #f0f1f2 solid 1px;

  .error-msg {
    height: 17px;
    font-size: 12px;
    font-weight: 400;
    line-height: 17px;
    color: #ff3c16;
  }

  .step-box {
    margin-top: 20px;
    margin-bottom: 12px;

    .step-index {
      display: inline-block;
      width: 20px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      background: #d9e9ff;
      border-radius: 20px;
    }

    .step-title {
      display: inline-block;
      height: 20px;
      margin-left: 4px;
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      color: #17181a;

      &.mr5 {
        margin-right: 5px;
      }
    }

    .info-icon {
      // position: relative;
      // top: 1px;
      font-size: 13px;
      color: #969799;
    }
  }

  .create {
    flex: 1;

    .digital-human-list {
      width: 485px;
      height: 298px;
      padding: 16px 15px;
      overflow-y: hidden;
      background: #fff;
      border: 1px solid #f0f1f2;
      border-radius: 8px;

      .scroll-box {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(105px, 105px));
        gap: 10px; /* 列与列之间的间距 */
        height: 100%;
        overflow-y: auto;

        &::-webkit-scrollbar {
          display: none;
        }

        .grid-item {
          width: 105px;
          height: 139px;
          background: linear-gradient(135deg, #fafafa 0%, #e6e6e6 100%);
          border-radius: 4px;

          &:hover {
            border: 1px solid #1777ff;
          }

          &.active {
            border: 2px solid #1777ff;

            img {
              border-radius: 2px;
            }
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 4px;
          }
        }
      }
    }

    .compose-preview-btn {
      .flex-center;

      width: 485px;
      height: 36px;
      margin: 20px auto 35px;
      font-size: 14px;
      font-weight: 500;
      color: #1777ff;
      background: #fff;
      border: 1px solid #1777ff;
      border-radius: 6px;
    }
  }

  .preview {
    flex: 1;
    // width: 577px;
    height: 100%;
    background: #f7f8fa;

    .preview-init {
      flex: 1;
      .flex-center;

      flex-direction: column;
      height: 100%;

      .preview-title {
        height: 20px;
        margin-top: 10px;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        color: #333;
      }

      .preview-tip {
        height: 17px;
        font-size: 12px;
        font-weight: 400;
        line-height: 17px;
        color: #636466;
      }
    }

    .preview-train {
      display: flex;
      justify-content: center;
      width: 100%;
      padding-bottom: 35px;

      .train-img {
        width: 100%;
        height: 608px;
        margin-bottom: 21px;
      }

      .preview-content {
        width: 444px;

        .preview-footer {
          display: flex;
          justify-content: center;
          height: 50px;

          .preview-input {
            box-sizing: border-box;
            width: 317px;
            height: 36px;

            & > .ant-input {
              height: 27px;
            }

            &.error {
              .ant-input::placeholder,
              .ant-input-show-count-suffix {
                color: #ff3c16;
              }
            }
          }

          .preview-btn {
            width: 111px;
            height: 36px;
            margin-left: 16px;
          }
        }
      }
    }
  }
}
