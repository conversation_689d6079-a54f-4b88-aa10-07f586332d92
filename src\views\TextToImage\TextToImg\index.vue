<script setup lang="ts">
  import { ref, onMounted, reactive, nextTick, watch } from 'vue';
  import sendBtn from '@/assets/image/base/pictures/sendbtn.png';
  import sendHover from '@/assets/image/base/pictures/sendHover.png.png';
  import sendDisable from '@/assets/image/base/pictures/sendDisable.png';

  import TextToImg from '@/assets/image/base/pictures/textToImg.png';
  import download from '@/assets/image/base/pictures/download.png';
  import { WebSocketClient as webSocketClass } from '@/utils/ws';
  import config from '@/config';
  import { getLocalItem } from '@/utils/common';
  import { useThrottle } from '@/hooks/useThrottle';
  import { message } from 'ant-design-vue';

  interface Message {
    type: 'question' | 'answer';
    text: string;
    answer?: string;
    picture_url?: string;
    icon?: string;
    invalidText?: string;
  }
  interface ResponseData {
    picture_url?: string;
    code: number;
    answer?: string;
  }

  const defaultText = '帮我画一张有老人、狗、海边、惬意生活的图片';
  // 输入框内容
  const inputText = ref(defaultText);
  let hasErrorMessage = false; // 错误消息标志位
  const loading = ref(false); // 图片加载标志位
  const sendBtnHover = ref(false);
  const msgBoxRef = ref<any>(undefined);
  // 消息列表
  const state = reactive({
    messageList: [
      {
        type: 'answer',
        text: '你好，欢迎使用文生图功能，输入你的描述，让我们一起创造属于我们的图像世界吧，你可以发送输入框的内容试一下',
      },
    ] as Message[], // 消息列表
  });

  // 创建 WebSocket 实例
  const textToImageWs = new webSocketClass(config.textToImage, (data: ResponseData) => {
    getMsgAndVideoList(data);
  });

  const getParams = (param: any) => {
    const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');
    return {
      user_id: userId,
      model: 'shuke-8b-dpo',
      topic_id: '4e11fd31-99ac-452a-92e8-4db348669a75',
      question: inputText.value,
      ...param,
    };
  };

  const getMsgAndVideoList = (data: ResponseData) => {
    const { picture_url, code, answer } = data;
    const isError = [403, 500, 501, 502, 503, 505, 506, 508, 510].includes(code);

    if (isError) {
      if (!hasErrorMessage) {
        state.messageList.push({
          type: 'answer',
          text: '没能明白您的意思，请重新提问~',
        });
        hasErrorMessage = true; // 设置标志位为 true
      }

      return;
    }

    if (code === 200) {
      loading.value = true; // 重置标志位
      state.messageList.push({
        type: 'answer',
        text: '正在绘制生成',
        icon: 'loading',
      });
    }

    if (code === 201) {
      // 检测最新一条数据是否包含 icon 字段
      const lastMessage = state.messageList[state.messageList.length - 1];
      if (lastMessage && lastMessage.icon) {
        // 如果包含 icon 字段，将其从消息列表中删除
        state.messageList.pop();
      }
      // 将回答加入消息列表
      if (picture_url) {
        if (picture_url === 'fail to create image') {
          state.messageList.push({
            type: 'answer',
            text: '图片生成失败，稍后再试吧',
          });
        } else {
          state.messageList.push({
            type: 'answer',
            text: '已根据你的描述生成图片，快点击查看吧',
            picture_url: picture_url,
          });
        }
      } else {
        if (answer) {
          const lastMessage = state.messageList.slice(-1)[0];
          if (lastMessage && lastMessage.type === 'answer') {
            // 如果最后一条消息是回答，追加内容
            lastMessage.invalidText += answer;
          } else {
            // 否则新增一条回答
            state.messageList.push({
              type: 'answer',
              text: '你的描述和图片生成无关，我只能处理和图片相关的问题，请重新输入有关图片的描述试试吧',
              invalidText: answer,
            });
          }
        }
      }
    }

    if (code === 202) {
      loading.value = false; // 重置图片加载标志位
      hasErrorMessage = false; // 重置错误消息标志位
    }
  };

  const setInputValue = (value: string) => {
    inputText.value = value;
  };

  // 发送消息
  const sendMessage = useThrottle((text?: string) => {
    const value = typeof text === 'string' ? text : inputText.value;
    if (!value || !value.trim()) {
      message.warning('请输入描述内容');
      return;
    }
    const question = text || inputText;

    setTimeout(() => {
      inputText.value = '';
    }, 0);
    loading.value = true; // 设置加载状态为 true

    // 将提问加入消息列表
    state.messageList.push({
      type: 'question',
      text: typeof question === 'string' ? question : question.value,
    });

    textToImageWs?.send(getParams({ question: question }));
  }, 3000);

  const downloadImage = async (picture_url: string) => {
    const imageURL = picture_url;
    const response = await fetch(imageURL);
    const blob = await response.blob();
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'downloaded_image.jpg'; // 为下载的图片指定一个文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  watch(state.messageList, () => {
    console.log(1111111, msgBoxRef.value);
    nextTick(() => {
      // (msgBoxRef.value as HTMLElement).scrollTo({ left: 0, top: 10000 });
      (msgBoxRef.value as HTMLElement).scrollTo({ left: 0, top: msgBoxRef.value.scrollHeight });
    });
  });

  // 页面加载时初始化
  onMounted(() => {
    textToImageWs.connect();
  });
</script>
<template>
  <div class="text-to-image-container">
    <!-- 消息框 -->
    <div ref="msgBoxRef" class="msg-box">
      <template v-for="(item, i) in state.messageList" :key="i">
        <!-- 问题展示 -->
        <div v-if="item.type === 'question'" class="question-box">
          <span class="text">{{ item.text }}</span>
        </div>
        <!-- 回答展示 -->
        <div v-else-if="item.type === 'answer'" class="answer-box">
          <div class="text-right">
            <div style="display: flex; justify-content: flex-start; align-items: center">
              <template v-if="item.icon === 'loading'">
                <div class="loader" style="margin-right: 10px"></div>
              </template>
              <span>{{ item.text }}</span>
            </div>
            <div v-if="item.picture_url" class="image-container">
              <!-- <span>{{ item.text }}</span> -->
              <a-image style="height: 320px; width: 320px" class="AImg" :src="item.picture_url" alt="" />
              <a-image
                class="download-btn"
                :src="download"
                alt=""
                :preview="false"
                @click="downloadImage(item.picture_url)"
              />
            </div>
          </div>
        </div>
      </template>
    </div>
    <!-- 聊天框 -->
    <div class="chat-box">
      <div class="tip-box">
        <a-image class="tip-icon" :src="TextToImg" alt="" :preview="false" />
        <span class="title">文生图</span>
        <span class="divider"> | </span>
        <span class="text">描述你想象的图片、角色、场景···AI帮你画图</span>
      </div>
      <div class="input-box">
        <a-textarea
          v-model:value="inputText"
          class="custom-textarea"
          placeholder="输入你的描述，继续创作图片"
          style="color: #17181a"
          :disabled="loading"
          :auto-size="{ minRows: 1, maxRows: 4 }"
          @change="
            (e: any) => {
              setInputValue(e.target.value);
            }
          "
          @press-enter="
            (e: any) => {
              e.preventDefault();
              sendMessage(inputText);
            }
          "
        />

        <a-image
          class="send-icon"
          :src="loading ? sendDisable : sendBtnHover ? sendHover : sendBtn"
          alt=""
          :preview="false"
          :style="{ cursor: loading ? 'not-allowed' : 'pointer' }"
          @mouseenter="sendBtnHover = true"
          @mouseleave="sendBtnHover = false"
          @click="sendMessage(inputText)"
        />
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
  .text-to-image-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: calc(100vh - 240px);

    .msg-box {
      width: 60%;
      padding: 10px 0;
      height: 100%;
      padding: 25px 25px 0 25px;
      overflow-y: auto;

      .answer-box {
        width: 100%;
        display: flex;
        text-align: start;
        margin-bottom: 10px;
        border: 1px solid #ebebeb;
        background: #ffffff;
        box-shadow: 0px 4px 8px 0px rgba(216, 227, 243, 0.25);
        border-radius: 12px 12px 12px 0px;

        .text-right {
          max-width: 100%;
          padding: 10px 15px;
          display: inline-block;
          background: #ffffff;
          border-radius: 12px 12px 12px 0px;
          text-align: left;
          .image-container {
            position: relative;
            width: 100%;

            .AImg {
              width: 320px;
              height: 320px;
              padding: 16px 20px;
              border-radius: 8px;
              object-fit: cover; /* 确保图片适应容器 */
            }
            .download-btn {
              position: absolute;
              // left: 50%;
              // transform: translateX(-50%);
              bottom: 10px;
              width: 32px;
              height: 32px;
              border-radius: 8px;
            }
          }

          > span {
            font-size: 15px;
            font-weight: 400;
            font-family:
              PingFangSC,
              PingFang SC;
            color: #17181a;
            line-height: 24px;

            font-style: normal;
          }
        }
      }

      .question-box {
        margin: 10px 0;
        width: 100%;
        display: flex;
        flex-direction: row-reverse;
        text-align: start;

        > span {
          max-width: 90%;
          padding: 10px 15px;
          display: inline-block;
          background: #dceefd;
          border-radius: 12px 12px 0px 12px;
          border: 1px solid #ffffff;
          opacity: 0.9;
          font-size: 16px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          color: #17181a;
          line-height: 24px;
          text-align: justify;
          font-style: normal;
        }
      }

      // /* 滚动条整体部分 */
      &::-webkit-scrollbar {
        height: 6px; /* 滚动条高度 */
        width: 6px; /* 滚动条宽度 */
      }
      // /* 滚动条滑块 */
      &::-webkit-scrollbar-thumb {
        background-color: transparent; /* 滑块颜色 */
        border-radius: 10px;
      }
      &:hover::-webkit-scrollbar-thumb {
        background-color: #888; /* 滑块颜色 */
      }
    }

    .chat-box {
      width: 60%; /* 占满页面宽度 */
      box-sizing: border-box; /* 包括内边距和边框 */
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      height: auto;
      background: #f2f8ff;
      border-radius: 12px;

      .tip-box {
        width: 100%;
        height: 44px;
        padding: 10px 20px;

        .tip-icon {
          width: 16px;
          height: 15px;
        }
        .title {
          width: 42px;
          height: 20px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #1777ff;
          line-height: 20px;
          text-align: right;
          font-style: normal;
          padding: 0 2px;
        }
        .divider {
          width: 1px;
          height: 21px;
          color: #c9dfff;
          border-radius: 1px;
        }
        .text {
          width: 331px;
          height: 24px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 13px;
          color: #646566;
          line-height: 24px;
          text-align: left;
          font-style: normal;
        }
      }

      .input-box {
        position: relative;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: auto;
        background: #ffffff;
        box-shadow: 0px 4px 8px 0px rgba(184, 196, 213, 0.2);
        border-radius: 12px;
        border: 1px solid #e9edf2;
        :deep(.custom-textarea) {
          position: relative;
          box-sizing: border-box;
          height: auto;
          padding: 10px;
          font-family: PingFangSC, 'PingFang SC';
          font-size: 16px;
          font-weight: 400;
          color: #fff;
          background: rgb(255 255 255 / 20%);
          border: 1px solid #e9edf2;
          border-radius: 12px;
          overflow: hidden; /* 隐藏滚动条 */
          resize: none; /* 禁止用户调整大小 */

          box-shadow: 0px 4px 8px 0px rgba(184, 196, 213, 0.2);

          /* 隐藏滚动条 */
          &::-webkit-scrollbar {
            display: none;
          }

          /* 占位符样式 */
          &::placeholder {
            color: #cccccc;
          }
        }

        :deep(.ant-image:nth-of-type(1)) {
          position: absolute;
          right: 16px; /* 第二个 .ant-image 距离右侧 10px */
          bottom: 8px;
          width: 32px;
          height: 32px;
          cursor: pointer;
        }
      }
    }
    .loader {
      width: 24px;
      aspect-ratio: 1;
      border-radius: 50%;
      background:
        radial-gradient(farthest-side, #1777ff 94%, #0000) top/3px 3px no-repeat,
        conic-gradient(#0000 30%, #1777ff);
      -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 3px), #000 0);
      animation: l13 1s infinite linear;
    }
    @keyframes l13 {
      100% {
        transform: rotate(1turn);
      }
    }
  }
</style>
