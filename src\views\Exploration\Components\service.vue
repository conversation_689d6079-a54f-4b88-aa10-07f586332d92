<script setup lang="ts">
  import { reactive, onMounted } from 'vue';
  import { fetchServiceList, fetchServiceListByUser } from '@/api/exploration';
  import type { IServiceItem } from '@/interface/exploration';
  import { useRoute } from 'vue-router';
  interface IProps {
    ids: string[];
    visible: boolean;
    type: 'add' | 'change';
  }
  const props = withDefaults(defineProps<IProps>(), {
    type: 'add',
  });

  const emits = defineEmits<{
    (event: 'cancel'): void;
    (event: 'ok', value: { service: IServiceItem; type: 'add' | 'change' }): void;
  }>();
  const route = useRoute();
  const manageList: IServiceItem[] = reactive([]);
  const userList: IServiceItem[] = reactive([]);
  const DEFAULT_STATE = {
    activeKey: 1,
    serviceId: '',
  };
  const state = reactive({ ...DEFAULT_STATE });

  const handleCancel = () => {
    emits('cancel');
  };

  const handleOk = () => {
    const service = [...userList, ...manageList].find((item) => item.id === state.serviceId) as IServiceItem;
    emits('ok', { service, type: props.type });
  };

  const clearState = () => {
    Object.assign(state, { ...DEFAULT_STATE });
  };

  const fetchServiceListByUserReq = async () => {
    const data: IServiceItem[] = await fetchServiceListByUser({
      exp_temp: route.path === '/ocr/detail' ? 'ocr' : 'text_chat',
    });
    userList.length = 0;
    userList.push(...data);
  };

  const fetchServiveListReq = async () => {
    const data: IServiceItem[] = await fetchServiceList({
      exp_temp: route.path === '/ocr/detail' ? 'ocr' : 'text_chat',
    });
    manageList.length = 0;
    manageList.push(...data);
  };

  onMounted(() => {
    fetchServiceListByUserReq();
    fetchServiveListReq();
  });

  defineExpose({
    clearState,
  });
</script>

<template>
  <a-modal centered :visible="props.visible" :width="'40%'" @cancel="handleCancel">
    <template #title>
      <div class="title">
        <exclamation-circle-outlined class="title-icon" />
        <span style="margin-right: 10px">{{ `${props.type === 'add' ? '添加' : '切换'}服务` }}</span>
      </div>
    </template>
    <template #footer>
      <slot name="footer">
        <a-button @click="handleCancel">
          <span>取消</span>
        </a-button>
        <a-button type="primary" @click="handleOk">
          <span>{{ '立即体验' }}</span>
        </a-button>
      </slot>
    </template>
    <a-tabs v-model:active-key="state.activeKey">
      <a-tab-pane :key="1" tab="预置服务">
        <template v-if="manageList.length">
          <a-radio-group v-model:value="state.serviceId">
            <a-radio v-for="item in manageList" :key="item.id" :disabled="ids.includes(item.id)" :value="item.id">{{
              item.service_name
            }}</a-radio>
          </a-radio-group>
        </template>
        <a-empty v-else />
      </a-tab-pane>
      <a-tab-pane :key="2" tab="我的服务">
        <template v-if="userList.length">
          <a-radio-group v-model:value="state.serviceId">
            <a-radio v-for="item in userList" :key="item.id" :disabled="ids.includes(item.id)" :value="item.id">{{
              item.service_name
            }}</a-radio>
          </a-radio-group>
        </template>
        <a-empty v-else />
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<style scoped lang="less">
  :deep(.ant-radio-group) {
    display: flex;
    flex-direction: column;

    > label {
      margin-bottom: 15px;
    }
  }
</style>
