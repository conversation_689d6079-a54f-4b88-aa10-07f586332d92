<script setup lang="ts">
  import { ref } from 'vue';
  import { Tooltip, message } from 'ant-design-vue';
  import { QuestionCircleFilled } from '@ant-design/icons-vue';
  import Interactive from './Interactive.vue';
  import { crateTask } from '@/api/model';
  import { type ICreateTaskProps } from '@/interface/model';
  import { useRouter } from 'vue-router';
  import type { IModelFormState } from '@/interface/model';
  const type = ref(0);
  const interactiveRef = ref();
  interface IProps {
    model: Partial<IModelFormState>;
    closeVisible: () => void;
  }
  const props = defineProps<IProps>();
  const router = useRouter();
  const loading = ref(false);
  const onConfirm = async () => {
    await interactiveRef.value.validateFields();
    loading.value = true;
    const data = JSON.parse(JSON.stringify(interactiveRef.value.formState));
    const { stage, finetuning_type, train_parameter } = data;
    const { general_parameters } = train_parameter;
    if (isNaN(general_parameters.learning_rate) || Number(general_parameters.learning_rate) < 0) {
      message.warn('学习率格式错误，请输入正数');
      return;
    }
    const method_parameter = {
      stage: stage,
      finetuning_type: finetuning_type,
    };
    const train_parameter_flat = { ...general_parameters };
    Object.keys(interactiveRef.value.disableState).forEach((key) => {
      if (interactiveRef.value.disableState[key]) {
        Object.assign(train_parameter_flat, { ...train_parameter[key] });
      }
    });
    data.output_name = data.model_name;
    delete data.stage;
    delete data.finetuning_type;
    delete data.train_parameter;
    delete data.model_name;

    if (data?.run_command) data.run_command = data.run_command.split('\n').map((e: string) => e.trim());

    const computed_type = train_parameter_flat.computed_type;
    if (['bf16', 'fp16'].includes(computed_type)) {
      delete train_parameter_flat.computed_type;
      train_parameter_flat[computed_type] = true;
    }
    const params: ICreateTaskProps = {
      model_id: props.model.id,
      method_parameter,
      ...data,
      dataset: data.dataset.join(','),
      train_parameter: train_parameter_flat,
      resource: interactiveRef.value.resources,
    };
    try {
      await crateTask(params);
      message.success('创建成功');
      props.closeVisible();
      loading.value = true;
      router.push({ path: '/train' });
    } catch {
      // message.warn(`创建失败 ${e}`)
    }
  };
</script>

<template>
  <div class="flex flex-col h-100%">
    <div class="flex-1 overflow-y-sorcll container">
      <a-radio-group v-model:value="type" style="margin-bottom: 16px">
        <a-radio-button :value="0">
          交互式配置
          <Tooltip>
            <template #title>
              提供直观的图形用户界面（GUI）等可视化界面，用户依据界面提示输入指令、设置参数或选择选项，轻松创建模型训练任务。
            </template>
            <QuestionCircleFilled />
          </Tooltip>
        </a-radio-button>
        <!-- <a-radio-button :value="1">
          脚本式配置
          <Tooltip>
            <template #title>
              用户编写包含系列指令与操作步骤的脚本，脚本执行时按预定顺序自动完成配置，高效便捷。
            </template>
            <QuestionCircleFilled />
          </Tooltip>
        </a-radio-button> -->
      </a-radio-group>
      <Interactive v-if="type === 0" ref="interactiveRef" :model="props.model" />
    </div>
    <div class="h-36px mt-10px flex flex-justify-end">
      <a-button type="primary" :loading="loading" @click="onConfirm">确定</a-button>
    </div>
  </div>
</template>

<style scoped lang="less">
  .container {
    height: calc(100% - 36px);
    overflow-y: scroll;
    .overflow-scroll;
  }
</style>
