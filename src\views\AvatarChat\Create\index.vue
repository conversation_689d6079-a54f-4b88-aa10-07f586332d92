<script setup lang="ts">
  import { ref } from 'vue';

  import { PlusOutlined, LoadingOutlined, UploadOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';

  import { getLocalItem } from '@/utils/common';
  import { getModeDigitals, getPreview, strtPretrain, uploadDigital } from '@/api/avatarChat';
  import Step from './Step.vue';
  import { UploadAvatarTips, TrainModes, theme } from './constant';
  import type { DigitalHumanItemProps, StepProps } from './constant';

  // import { Loading } from '@/components';
  import DefaultDigitalPreviewImg from '@/assets/image/base/pictures/digital-preview-icon.png';

  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');

  const props = defineProps<{
    handleCloseModal: () => void;
  }>();

  const isOpenModal = ref(false);
  const digitalHumanLoading = ref(false);
  const loading = ref(false);
  const pageLoading = ref(false);
  const trainMode = ref('fast');
  const digitalList = ref<{ url: string; gender: string }[]>([{ url: '', gender: '' }]);
  const selectedDigitalMan = ref({ index: 0, url: '', gender: '' });
  const uploadAvatarUrl = ref('');
  const preViewUrl = ref('');
  const name = ref('');
  const isNameEntered = ref(true);
  const isAvatarUploaded = ref(true);

  const openModal = () => {
    isOpenModal.value = true;
    trainMode.value = 'fast';
    uploadAvatarUrl.value = '';
    preViewUrl.value = '';
    selectedDigitalMan.value = { index: 0, url: '', gender: '' };
    isNameEntered.value = true;
    isAvatarUploaded.value = true;
    queryDigitalHumanList();
  };

  defineExpose({
    openModal,
  });

  const handlePreview = () => {
    console.log('preview');
    isAvatarUploaded.value = Boolean(uploadAvatarUrl.value);
    if (!uploadAvatarUrl.value) {
      return;
    }

    pageLoading.value = true;
    getPreview({
      category: trainMode.value,
      source_path: uploadAvatarUrl.value,
      target_path: selectedDigitalMan.value.url,
      user_id: userId,
    })
      .then((url: string) => {
        preViewUrl.value = url as string;
      })
      .catch(() => {
        console.log('预览失败');
        // message.error('预览失败');
      })
      .finally(() => {
        pageLoading.value = false;
      });
  };

  const handleTrain = async () => {
    isNameEntered.value = Boolean(name.value);
    if (!name.value) {
      return;
    }

    pageLoading.value = true;
    const isCartoon = selectedDigitalMan.value.index === digitalList.value.length - 1;
    strtPretrain({
      name: name.value,
      category: trainMode.value,
      image_url: preViewUrl.value,
      source_path: uploadAvatarUrl.value,
      is_cartoon: isCartoon,
      target_path: isCartoon ? '' : selectedDigitalMan.value.url,
      gender: isCartoon ? '' : selectedDigitalMan.value.gender,
      user_id: userId,
    })
      .then(() => {
        props.handleCloseModal();
        isOpenModal.value = false;
        message.success('已经开始训练');
      })
      .catch(() => {
        message.error('训练失败');
      })
      .finally(() => {
        pageLoading.value = false;
      });
  };

  const handleTrainModeChange = (value: string) => {
    trainMode.value = value;
  };

  const handleDigitalModeChange = (index: number, item: DigitalHumanItemProps) => {
    selectedDigitalMan.value = { index, ...item };
    // console.log(selectedDigitalMan.value);
  };

  const handleNameChange = (value = '') => {
    const newName = value;
    console.log(newName);
    isNameEntered.value = Boolean(newName);
    name.value = newName;
  };

  const handleOk = () => {
    isOpenModal.value = false;
  };

  const handleCancel = () => {
    isOpenModal.value = false;
  };

  const queryDigitalHumanList = async () => {
    digitalHumanLoading.value = true;
    getModeDigitals()
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      .then((data: any) => {
        digitalList.value = data;
        selectedDigitalMan.value = { index: 0, ...data[0] };
      })
      .finally(() => {
        digitalHumanLoading.value = false;
      });
  };

  const uploadProps = {
    beforeUpload: (file: File) => {
      const is = ['image/png', 'image/jpg', 'image/jpeg'].includes(file.type);
      if (!is) {
        message.error('请上传jpg、jpeg、png格式图片');
      }
      return is;
    },
    customRequest: async (detail: { file: File }) => {
      const file = detail.file;
      const formData = new FormData();
      formData.append('face', file);
      loading.value = true;
      uploadDigital(formData, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      })
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .then((data: any) => {
          uploadAvatarUrl.value = data?.[0];
          isAvatarUploaded.value = data?.[0] ? true : false;
        })
        .catch(() => {
          message.error('上传失败，请上传五官清晰的人脸照片');
        })
        .finally(() => {
          loading.value = false;
        });
    },
    multiple: false,
    fileList: [],
    accept: 'image/png,image/jpg,image/jpeg',
    showUploadList: false,
  };
</script>

<template>
  <a-modal
    :open="isOpenModal"
    title="创建数字人"
    :footer="null"
    :width="1122"
    :mask-closable="false"
    centered
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-spin :spinning="pageLoading" size="large" tip="加载中..." wrapper-class-name="custom-spin">
      <div class="create-digital-man-modal">
        <div class="create">
          <Step :step="1" title="训练方式" />
          <a-config-provider :theme="theme">
            <a-radio-group v-model:value="trainMode" class="custom-radio-group">
              <div
                v-for="{ value, title, tips, disabled } in TrainModes"
                :key="title"
                :class="{
                  'radio-item': true,
                  selected: trainMode === value,
                  disabled: disabled,
                }"
                @click="!disabled && handleTrainModeChange(value)"
              >
                <a-radio :disabled="disabled" :value="value">
                  {{ title }}
                </a-radio>
                <div class="desc">{{ tips }}</div>
              </div>
            </a-radio-group>
          </a-config-provider>

          <Step :step="2" title="数字人形象" />

          <div class="digital-human-list">
            <a-config-provider :theme="theme">
              <div class="scroll-box">
                <div
                  v-for="({ url, gender }, index) in digitalList.filter((item) => item.url)"
                  :key="url"
                  :class="{
                    'grid-item': true,
                    active: selectedDigitalMan.index === index,
                  }"
                  @click="handleDigitalModeChange(index, { url, gender })"
                >
                  <img v-if="url" :src="url" alt="" />
                </div>
              </div>
            </a-config-provider>
          </div>

          <div class="custom-upload-avatar">
            <Step :step="3" title="上传头像" :tooltips="UploadAvatarTips" />
            <p class="avatar-format-tip">仅支持上传清晰人脸照（jpg、jpeg、png格式）</p>
            <a-upload
              v-bind="uploadProps"
              list-type="picture-card"
              :class="{ 'avatar-uploader': true, error: !isAvatarUploaded }"
            >
              <template #default>
                <div v-if="uploadAvatarUrl">
                  <div class="upload-preview">
                    <img v-if="uploadAvatarUrl" :src="uploadAvatarUrl" alt="avatar" class="avatar-icon" />
                    <div class="upload-overlay">
                      <UploadOutlined class="re-upload-icon" />

                      重新上传
                    </div>
                  </div>
                </div>
                <div v-else>
                  <div class="upload-icon-box">
                    <button type="button" class="upload-button">
                      <template v-if="loading">
                        <LoadingOutlined />
                      </template>
                      <template v-else>
                        <PlusOutlined class="upload-plus-icon" />
                      </template>
                    </button>
                    <span v-if="!isAvatarUploaded" class="error-msg">请上传头像</span>
                  </div>
                </div>
              </template>
            </a-upload>
          </div>

          <a-button :disabled="pageLoading" class="compose-preview-btn" type="primary" ghost @click="handlePreview">
            合成预览
          </a-button>
        </div>

        <div class="preview">
          <template v-if="preViewUrl === ''">
            <div class="preview-init">
              <img :src="DefaultDigitalPreviewImg" alt="" />
              <div class="preview-title">数字人预览</div>
              <div class="preview-tip">请在左侧配置好参数后合成预览</div>
            </div>
          </template>
          <template v-else>
            <div class="preview-train">
              <div class="preview-content">
                <Step :step="4" title="数字人预览" />
                <img class="train-img" :src="preViewUrl" alt="" />
                <div class="preview-footer">
                  <a-input
                    v-model="name"
                    :status="!isNameEntered ? 'error' : ''"
                    :class="{ 'preview-input': true, error: !isNameEntered }"
                    placeholder="请输入数字人名称"
                    :maxlength="10"
                    show-count
                    @change="(e: Event) => handleNameChange((e.target as HTMLInputElement).value)"
                  />
                  <a-button :disabled="pageLoading" type="primary" class="preview-btn" @click="handleTrain">
                    确认合成
                  </a-button>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<style lang="less" src="./custom.less"></style>
<style lang="less" src="./custom-antd.less"></style>
