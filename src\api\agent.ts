import type { IFetchAgentList, IMessage, IUpdateAgentProps } from '@/interface/agent';
import request from '@/utils/request';

export function fetchAgentList(data: IFetchAgentList) {
  return request.$Axios.get(`/agent/version/latest/list`, data);
}
// 创建智能体
export const createAgent = () => {
  return request.$Axios.post(`/agent/version/add`);
}
// 智能体详情
export const agentVersionDetail = (version_id: string) => {
  return request.$Axios.get(`/agent/version/latest/detail/${version_id}`);
}
// 发布的智能体详情
export const getAgentDetail = (version_id: string) => {
  return request.$Axios.get(`/agent/detail/${version_id}`);
}
// 编辑智能体
export const updateAgent = (version_id: string, data: IUpdateAgentProps) => {
  return request.$Axios.post(`/agent/version/update/${version_id}`, data);
}
// 发布智能体
export const publishAgent = (version_id: string) => {
  return request.$Axios.post(`/agent/version/publish/${version_id}`,);
}
// 智能体对话
export const agentChct = (agent_id: string, data: { question: string; history: IMessage[] }) => {
  return request.$Axios.post(`/agent/chat/${agent_id}`, data);
}
// 删除智能体
export const delAgent = (version_id: string) => {
  return request.$Axios.del(`/agent/version/delete/${version_id}`,);
}
// 检查智能体名称是否重复
export const checkAgentName = (name: string) => {
  return request.$Axios.post(`/agent/name/check`, { name });
};
