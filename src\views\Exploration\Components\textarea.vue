<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import { ArrowUpOutlined, StopOutlined } from '@ant-design/icons-vue';
  interface IProps {
    value?: string;
    placeholder?: string;
    disabled: boolean;
  }
  const props = withDefaults(defineProps<IProps>(), {
    value: '',
    placeholder: '给模型发送消息',
  });
  const message = ref(props.value);
  const textareaRef = ref();
  const forbidden = computed(() => props.disabled || (!message.value && !state.start));
  const state: Record<string, boolean> = reactive({
    focus: false,
    loading: false,
    start: false, // 是否开始回答
  });
  const emits = defineEmits<{
    (event: 'send', value: string): void;
    (event: 'stop'): void;
  }>();
  const handleClick = (event: Event) => {
    if (state.start) {
      handleStop();
    } else {
      handleSend(event);
    }
  };
  const handleSend = (event: Event) => {
    event.preventDefault();
    (event.target as HTMLElement).blur();
    state.loading = true;
    state.start = true;
    emits('send', message.value);
    message.value = '';
  };
  const handleStop = () => {
    emits('stop');
    state.start = false;
  };
  const changeState = (key: string, bool: boolean) => {
    state[key] = bool;
  };
  defineExpose({
    changeState,
    state,
  });
</script>

<template>
  <div class="custom-textarea" :class="{ active: state.focus }">
    <a-textarea
      ref="textareaRef"
      v-model:value="message"
      :default-value="props.value"
      :rows="3"
      allow-clear
      show-count
      :maxlength="500"
      :auto-size="{ minRows: 2, maxRows: 3 }"
      :placeholder="props.placeholder"
      @focus="state.focus = true"
      @blur="state.focus = false"
      @pressEnter="handleSend"
    />
    <div class="flex flex-justify-end m-t-30px p-b-5px">
      <a-button type="primary" :disabled="forbidden" :loading="state.loading" @click="handleClick">
        <StopOutlined v-if="state.start" />
        <ArrowUpOutlined v-else />
      </a-button>
    </div>
  </div>
</template>

<style scoped lang="less">
  .custom-textarea {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    padding: 0 10px;
    overflow: hidden;
    cursor: pointer;
    border: 1px solid #d9d9d9;
    background-color: #fff;
    border-radius: 10px;

    :deep(.ant-input) {
      border: none;

      &:focus {
        box-shadow: none;
      }
    }

    &:hover {
      border-color: #4096ff;
      box-shadow: 0 0 0 2px rgb(5 145 255 / 10%);
    }
  }

  .active {
    border-color: #4096ff;
    box-shadow: 0 0 0 2px rgb(5 145 255 / 10%);
  }
</style>
