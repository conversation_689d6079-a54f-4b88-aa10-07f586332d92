<script lang="ts" setup>
  import { ref } from 'vue';
  import TextToImg from './TextToImg/index.vue';
  import StyleTransfer from './styleTransfer/index.vue';

  const activeKey = ref('1');
</script>

<template>
  <a-tabs v-model:active-key="activeKey" class="tab-container">
    <a-tab-pane key="1" tab="文生图">
      <TextToImg v-if="activeKey === '1'" />
    </a-tab-pane>
    <a-tab-pane key="2" tab="图片风格转换" force-render><StyleTransfer v-if="activeKey === '2'" /></a-tab-pane>
  </a-tabs>
</template>

<style lang="less" scoped>
  .tab-container {
    :deep(.ant-tabs-nav) {
      margin-bottom: 0 !important;
    }
  }
</style>
