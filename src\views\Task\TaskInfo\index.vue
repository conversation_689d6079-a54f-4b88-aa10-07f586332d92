<!--
 * @Author: dengfusheng 
 * @Date: 2025-02-26 11:16:53
 * @LastEditTime: 2025-03-28 10:11:58
 * @LastEditors: <EMAIL>
 * @FilePath: \ai-platform\ai-platform-frontend\src\views\Task\TaskInfo\index.vue
 * @Description: 任务详情主页面
-->
<script setup lang="ts">
  import { nextTick, onBeforeUnmount, reactive, ref, watch, onMounted } from 'vue';
  import { Descriptions, Spin } from 'ant-design-vue';
  import { QuestionCircleOutlined } from '@ant-design/icons-vue';
  import Monitor from '@/views/Task/TaskInfo/Monitor.vue';
  import ModelTarget from '@/views/Task/TaskInfo/ModelTarget.vue';
  import { getTaskInfo, getTaskLogs } from '@/api/model';
  import { useRoute, useRouter } from 'vue-router';
  import {
    trainMethods,
    trainTypes,
    general_parameters,
    other_parameters,
    partial_parameters,
    parameter,
  } from '@/views/Model/Train/index.ts';
  import { TrainStatus } from '../index.js';
  import { DownOutlined, UpOutlined } from '@ant-design/icons-vue';
  import { VAceEditor } from 'vue3-ace-editor';
  import 'ace-builds/src-noconflict/theme-chrome';
  import 'ace-builds/src-noconflict/theme-monokai.js';
  import 'ace-builds/src-noconflict/mode-python.js';
  import 'ace-builds/src-noconflict/mode-sh.js';
  import { formatTime, getLocalItem, setLocalItem } from '@/utils/common.js';
  import Deploy from '@/views/Model/Deploy/index.vue';
  import type { IAceOptions } from '@/interface';

  const route = useRoute();
  const runCommand = ref(``);
  const aceEditor = ref<any>(null);

  const deployState = reactive({
    visible: route.query.type === 'deploy',
    loading: false,
  });

  interface ITaskInfo {
    task_name: string;
    id: string;
    description: string;
    dataset: string;
    status: string;
    run_time: number;
    train_parameter: string;
    output_name: string;
    model_name: string;
    // image_info: { name }:string;
    method_parameter: { finetuning_type: string; stage: string };
    docker_image: string;
    run_command: string[];
    created_at: string;
  }
  interface IState {
    langEnum: string[];
    activeKey: number;
    hyperparameter: string[][];
    taskInfo: ITaskInfo;
    expand: boolean;
    logStr: string;
    basicInfo: string[][];
    spinning: boolean;
    finished?: boolean;
    editor: unknown;
  }

  const state = reactive<IState>({
    langEnum: ['SQL', 'Python', 'sh'],
    activeKey: getLocalItem('taskinfotab') * 1 || 1,
    hyperparameter: [],
    taskInfo: {
      task_name: '',
      id: '',
      description: '',
      dataset: '',
      status: '',
      run_time: 0,
      train_parameter: '',
      output_name: '',
      model_name: '',
      docker_image: '',
      // image_info: { name }:'',
      method_parameter: { finetuning_type: '', stage: '' },
      run_command: [],
      created_at: '',
    },
    expand: true,
    logStr: '',
    basicInfo: [
      ['任务名/ID', ''],
      ['预训练模型', ''],
      ['训练方式', ''],
      ['运行状态', ''],
      ['运行时长', ''],
      ['输出模型名', ''],
    ],
    spinning: false,
    finished: undefined,
    editor: undefined,
  });

  const aceOptions: IAceOptions = {
    enableBasicAutocompletion: true,
    enableSnippets: true,
    enableLiveAutocompletion: true,
    tabSize: 4,
    enableEmmet: true,
    fontSize: 15,
    showPrintMargin: false, // 去除编辑器里的竖线
    highlightActiveLine: true,
    useWorker: true,
    readOnly: false,
  };

  onMounted(async () => {
    const { taskid } = route.query;
    console.log(taskid, 'taskid');
    if (taskid) {
      state.spinning = true;
      state.taskInfo = await getTaskInfo(taskid as string);
      const {
        task_name,
        id,
        description,
        dataset,
        status,
        run_time,
        train_parameter,
        output_name,
        model_name,
        // image_info: { name },
        method_parameter: { finetuning_type, stage },
        run_command,
      } = state.taskInfo;

      if (run_command) {
        run_command.forEach((str: string) => {
          runCommand.value += `${str}
`;
        });
      }
      state.basicInfo = [
        ['任务名/ID', task_name || id],
        ['预训练模型', model_name],
        [
          '训练方式',
          `${trainTypes.find((e) => e.value == finetuning_type)!.label} -- ${trainMethods.find((e) => e.value == stage)!.name}`,
        ],
        ['所选数据集', dataset],
        ['运行状态', TrainStatus.find((e) => e.value == status)?.label as string],
        ['运行时长', `${formatTime(run_time)}`],
        ['输出模型名', output_name],
        ['版本描述', description],
      ];
      state.hyperparameter = Object.entries(train_parameter || {});
      state.spinning = false;
    }
  });

  watch(
    [() => state.activeKey, () => state.finished],
    async ([activeKey, _finished]) => {
      const { taskid } = route.query;

      if (activeKey == 2 && taskid && (_finished === undefined || _finished === true)) {
        state.finished = false;
        const { finished, log } = await getTaskLogs(taskid as string);
        state.logStr = log;
        nextTick(() => {
          if (state.editor) {
            // @ts-expect-error
            const session = state.editor.getSession();
            const lastLine = session.getLength(); // 获取最后一行
            // @ts-expect-error
            state.editor.gotoLine(lastLine); // 将光标移动到最后一行
            // @ts-expect-error
            state.editor.scrollToLine(lastLine, true, true, () => {}); // 滚动到最后一行
          }
        });
        await new Promise((resolve) => setTimeout(resolve, 1000));
        if (finished === false) state.finished = true;
      }
      if (activeKey !== 2) state.finished = undefined;
    },
    { deep: true, immediate: true },
  );

  const editorInit = (editor: any) => (state.editor = editor);

  onBeforeUnmount(() => setLocalItem('taskinfotab', ''));
</script>

<template>
  <div class="task-info">
    <div class="flex justify-between items-center">
      {{ state.taskInfo?.task_name || '-------' }}
      <a-button v-if="state.taskInfo?.status === 'completed'" type="primary" @click="deployState.visible = true"
        >部署</a-button
      >
    </div>
    <a-tabs v-model:active-key="state.activeKey" @change="(key: any) => setLocalItem('taskinfotab', key)">
      <a-tab-pane :key="1" tab="任务详情">
        <div class="particulars overflow-scroll">
          <Spin :spinning="state.spinning">
            <div class="textbefo">基本信息</div>
            <Descriptions size="small" :column="2">
              <Descriptions.Item v-for="(d, l) in state.basicInfo" :key="l" :label="d[0]">
                {{ d[1] || '--' }}
              </Descriptions.Item>
              <!-- <Descriptions.Item /> -->
              <Descriptions.Item label="超参配置">
                <div class="details" @click="state.expand = !state.expand">
                  <span>详细信息</span>
                  <DownOutlined v-if="state.expand" />
                  <UpOutlined v-else />
                </div>
              </Descriptions.Item>
            </Descriptions>
            <div :class="{ hyperparameter: true, fewer: !state.expand }">
              <a-descriptions size="small" :column="4" bordered>
                <a-descriptions-item
                  v-for="(n, i) in state.hyperparameter"
                  :key="i"
                  :label="
                    ['bf16', 'fp16', 'fp32', 'pure_bf16'].includes(n[0])
                      ? `computed_type 计算配置`
                      : [
                          ...general_parameters,
                          ...other_parameters,
                          ...partial_parameters,
                          ...parameter.LoRA_parameters,
                          ...parameter.RLHF_parameters,
                          ...parameter.GaLore_parameters,
                          ...parameter.BAdam_parameters,
                        ].find((e: any) => e.value == n[0])?.label
                  "
                >
                  {{ n[1] !== undefined ? (['bf16', 'fp16', 'fp32', 'pure_bf16'].includes(n[0]) ? n[0] : n[1]) : '--' }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
            <div class="textbefo">计算资源配置</div>
            <Descriptions size="small" :column="2">
              <Descriptions.Item label="计算资源">{{ '公共资源' }}</Descriptions.Item>
              <Descriptions.Item>
                <Descriptions size="small" :column="1" layout="vertical">
                  <Descriptions.Item label="资源列表">
                    <a-table
                      :columns="[
                        { title: 'GPU', dataIndex: 'gpu' },
                        { title: 'CPU', dataIndex: 'cpu' },
                        { title: '内存', dataIndex: 'storage' },
                      ]"
                      :data-source="[{ key: '1', gpu: '1 * NVIDIA V100', cpu: 'Dynamic', storage: 'Dynamic' }]"
                      bordered
                      :pagination="false"
                    >
                    </a-table>
                  </Descriptions.Item>
                </Descriptions>
              </Descriptions.Item>
            </Descriptions>

            <div class="textbefo">镜像</div>
            <Descriptions size="small" :column="2">
              <Descriptions.Item label="镜像">{{ state.taskInfo.docker_image }}</Descriptions.Item>
            </Descriptions>

            <div class="textbefo">初始命令</div>
            <v-ace-editor
              v-model:value="runCommand"
              lang="sh"
              theme="monokai"
              :options="aceOptions"
              style="height: 300px; border: 1px solid #dbd3d3"
            />
          </Spin>
        </div>
      </a-tab-pane>
      <a-tab-pane :key="2" tab="任务日志">
        <v-ace-editor
          ref="aceEditor"
          v-model:value="state.logStr"
          :lang="state.langEnum[2].toLowerCase()"
          theme="monokai"
          :options="aceOptions"
          style="height: 100%; border: 1px solid #dbd3d3"
          @init="editorInit"
        />
      </a-tab-pane>
      <a-tab-pane :key="3" tab="任务监控">
        <div class="carousel">
          <div class="carousel-item">建议监控时长不低于 1 分钟，否则可能无法获取数据</div>
        </div>
        <Monitor
          v-if="state.taskInfo"
          :created="state.taskInfo?.created_at"
          :runTime="state.taskInfo?.run_time"
          :status="state.taskInfo?.status"
        >
        </Monitor>
      </a-tab-pane>
      <a-tab-pane :key="4" tab="模型指标监控">
        <div class="carousel">
          <div class="carousel-item">建议监控时长不低于 1 分钟，否则可能无法获取数据</div>
        </div>
        <ModelTarget :active-key="state.activeKey"></ModelTarget>
      </a-tab-pane>
    </a-tabs>
  </div>
  <a-drawer v-model:open="deployState.visible" width="800" :mask-closable="false">
    <template #title>
      部署
      <a-tooltip>
        <template #title>使用预置开源的模型并将其部署为在线服务，以进行实时的推理调用。</template>
        <QuestionCircleOutlined class="ml-5px" />
      </a-tooltip>
    </template>
    <Deploy :model="state.taskInfo" :close-visible="() => (deployState.visible = false)" />
  </a-drawer>
</template>

<style lang="less" scoped>
  .task-info {
    display: flex;
    flex-direction: column;
    height: 100%;

    > div:nth-child(1) {
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: 600;
    }

    .particulars {
      height: 100%;

      > div:nth-child(2n) {
        margin-bottom: 40px;
      }

      .hyperparameter {
        max-height: 1000px;
        overflow: hidden;
        transition: max-height, 0.5s;
      }

      .fewer {
        max-height: 0 !important;
        transition: max-height, 0.5s;
      }

      .details {
        display: flex;
        cursor: pointer;

        > span {
          margin-right: 10px;
        }
      }
    }

    // overflow-scroll

    .textbefo {
      position: relative;
      padding: 5px 0 5px 20px;
      // padding-left: 40px;
      margin: 10px 0 20px;
      font-size: 15px;
      font-weight: 600;
    }

    .textbefo::before {
      position: absolute;
      top: 9px;
      left: 0;
      display: inline-block;
      width: 5px;
      height: 16px;
      content: '';
      background: rgb(38 61 252 / 82%);
    }
  }

  :deep(.ant-descriptions-bordered) {
    width: 100% !important;
  }

  :deep(.ant-collapse) {
    width: 100%;

    .ant-collapse-header {
      padding: 0 16px !important;
    }
  }

  // :deep(.ant-tabs-tab) {
  //   margin: 0 !important;
  //   padding: 8px 16px;
  //   border: 1px solid;

  //   +.ant-tabs-tab {
  //     border-left: none;
  //   }
  // }

  // TAP 栏样式
  :deep(.ant-tabs-top) {
    flex: 1;
    height: 0;
  }

  :deep(.ant-tabs-content) {
    height: 100%;
  }
</style>
