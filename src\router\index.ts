import { getLocalItem } from '@/utils';
import { administratorIds } from '@/utils/enum';
import { createRouter, createWebHashHistory } from 'vue-router';
import { useStore } from '@/stores/universal';
import type { Component } from 'vue';

export interface ICustomRouterRaw {
  path: string;
  name: string;
  meta?: Meta;
  icon?: string;
  hidden?: boolean;
  children?: Children[];
  component?: () => Promise<Component>;
}

export interface Meta {
  breadcrumb: string;
}

export interface Children {
  path: string;
  name: string;
  hidden?: boolean;
  meta?: Meta;
  children?: Children[];
  component: () => Promise<Component>;
}
export const menuRouter: ICustomRouterRaw[] = [
  {
    path: '/',
    name: 'login',
    hidden: true,
    component: () => import('@/views/Login/index.vue'),
  },
  {
    path: '/home',
    name: 'home',
    meta: { breadcrumb: '首页' },
    hidden: true,
    component: () => import('@/views/Home/index.vue'),
  },
  {
    path: '/train',
    name: 'train',
    icon: '#icon-moxingxunlian',
    meta: { breadcrumb: '模型训练' },
    children: [
      {
        path: '',
        name: 'task_list',
        hidden: true,
        meta: { breadcrumb: '任务列表' },
        component: () => import('@/views/Task/index.vue'),
      },
      {
        path: 'detail',
        name: 'task_detail',
        meta: { breadcrumb: '训练任务' },
        hidden: true,
        component: () => import('@/views/Task/TaskInfo/index.vue'),
      },
    ],
  },
  {
    path: '/deploy',
    name: 'deploy',
    icon: '#icon-moxingbushu',
    meta: { breadcrumb: '模型部署' },
    children: [
      {
        path: '',
        name: 'deploy_list',
        hidden: true,
        meta: { breadcrumb: '任务列表' },
        component: () => import('@/views/Deploy/index.vue'),
      },
      {
        path: 'detail',
        name: 'server_detail',
        meta: { breadcrumb: '部署任务' },
        hidden: true,
        component: () => import('@/views/Deploy/Detail/index.vue'),
      },
    ],
  },
  {
    path: '/integration',
    name: 'integration',
    icon: '#icon-moxingjicheng',
    meta: { breadcrumb: '模型集成' },
    children: [
      {
        path: '/integration',
        name: 'integration_optimization',
        hidden: true,
        meta: { breadcrumb: '答策中枢' },
        component: () => import('@/views/Integration/index.vue'),
      },
      {
        path: '/history',
        name: 'integration_history',
        meta: { breadcrumb: '历史列表' },
        hidden: true,
        component: () => import('@/views/Integration/History/index.vue'),
      },
    ],
  },
  {
    path: '/exploration',
    name: 'exploration',
    icon: '#icon-tubiaozhizuosvg',
    meta: { breadcrumb: '体验中心' },
    children: [
      {
        path: '/exploration/avatarChat',
        name: 'avatarChat',
        meta: { breadcrumb: '数字人对话' },
        component: () => import('@/views/AvatarChat/index.vue'),
      },
      {
        path: '/text',
        name: 'text',
        hidden: !true,
        meta: { breadcrumb: '文本对话' },
        component: () => import('@/views/Exploration/index.vue'),
        children: [
          {
            path: '/text/detail',
            name: 'Text-detail',
            hidden: true,
            meta: { breadcrumb: '详情' },
            component: () => import('@/views/Exploration/Text/index.vue'),
          },
        ],
      },
      {
        path: '/TextToImage',
        name: 'TextToImage',
        hidden: !true,
        meta: { breadcrumb: '图片生成' },
        component: () => import('@/views/TextToImage/index.vue'),
      },
      {
        path: '/QuestionsAnswers',
        name: 'QuestionsAnswers',
        meta: { breadcrumb: '剑门关知识问答' },
        component: () => import('@/views/QuestionsAnswers/index.vue'),
      },
      {
        path: '/VirtualAmbassador',
        name: 'VirtualAmbassador',
        hidden: !true,
        meta: { breadcrumb: '虚拟形象大使' },
        component: () => import('@/views/VirtualAmbassador/index.vue'),
      },
      // {
      //   path: '/picture',
      //   name: 'picture',
      //   hidden: !true,
      //   meta: { breadcrumb: '文生图片' },
      //   component: () => import('@/views/Exploration/Picture/index.vue'),
      // },
      // {
      //   path: '/video',
      //   name: 'video',
      //   hidden: !true,
      //   meta: { breadcrumb: '文生视频' },
      //   component: () => import('@/views/Exploration/Video/index.vue'),
      // },
      {
        path: '/ocr',
        name: 'OCR',
        meta: { breadcrumb: 'OCR识别' },
        component: () => import('@/views/Exploration/index.vue'),
        children: [
          {
            path: '/ocr/detail',
            name: 'OCR-detail',
            hidden: true,
            meta: { breadcrumb: '详情' },
            component: () => import('@/views/Exploration/OCR/index.vue'),
          },
        ],
      },
      {
        path: '/VideoGeneration',
        name: 'VideoGeneration',
        meta: { breadcrumb: '视频生成' },
        component: () => import('@/views/VideoGeneration/index.vue'),
      },
      {
        path: '/ClassRoom',
        name: 'classRoom',
        meta: { breadcrumb: '虚拟课堂' },
        component: () => import('@/views/ClassRoom/index.vue'),
        children: [
          {
            path: '/ClassRoom/addEdit',
            name: 'ClassRoom-addEdit',
            hidden: true,
            meta: { breadcrumb: '' },
            component: () => import('@/views/ClassRoom/AddEdit/index.vue'),
          },
          {
            path: '/ClassRoom/answer',
            name: 'ClassRoom-answer',
            hidden: true,
            meta: { breadcrumb: '答疑' },
            component: () => import('@/views/ClassRoom/answer/index.vue'),
          },
          {
            path: '/ClassRoom/:id',
            name: 'ClassRoom-details',
            hidden: true,
            meta: { breadcrumb: '课程详情' },
            component: () => import('@/views/ClassRoom/Details/index.vue'),
          },
        ],
      },
    ],
  },
  {
    path: '/knowledge',
    name: 'Knowledge',
    icon: '#icon-k8s-jiqun',
    meta: { breadcrumb: '知识库' },
    component: () => import('@/views/Knowledge/index.vue'),
    children: [
      {
        path: '/knowledge/add',
        name: 'knowledge-add',
        meta: { breadcrumb: '创建知识库' },
        hidden: true,
        component: () => import('@/views/Knowledge/detail.vue'),
      },
      {
        path: '/knowledge/export/:id',
        name: 'knowledge-export',
        meta: { breadcrumb: '导入文件' },
        hidden: true,
        component: () => import('@/views/Knowledge/detail.vue'),
      },
      {
        path: '/knowledge/file/:id',
        name: 'knowledge-file',
        meta: { breadcrumb: '知识库文件' },
        hidden: true,
        component: () => import('@/views/Knowledge/file.vue'),
      },
    ],
  },
  {
    path: '/intelligent',
    name: 'Intelligent',
    icon: '#icon-zhinengti',
    meta: { breadcrumb: '智能体应用' },
    component: () => import('@/views/Intelligent/index.vue'),
    children: [
      {
        path: '/intelligent/:id',
        name: 'intelligent-detail',
        hidden: true,
        meta: { breadcrumb: '详情' },
        component: () => import('@/views/Intelligent/detail.vue'),
      },
      {
        path: '/intelligent/preview/:id',
        name: 'intelligent-preview',
        hidden: true,
        meta: { breadcrumb: '体验' },
        component: () => import('@/views/Intelligent/preview.vue'),
      },
    ]
  },
  {
    path: '/cluster-manage',
    name: 'cluster-management',
    icon: '#icon-k8s-jiqun',
    meta: { breadcrumb: '集群管理' },
    children: [
      {
        path: '/cluster-manage/kubernetes',
        name: 'Kubernetes',
        meta: { breadcrumb: '集群概览' },
        component: () => import('@/views/Kubernetes/index.vue'),
      },
      {
        path: '/cluster-manage/workload',
        name: 'workload',
        meta: { breadcrumb: '工作负载' },
        component: () => import('@/views/Cluster/Workload/index.vue'),
        children: [
          {
            path: '/cluster-manage/workload/:name',
            name: 'workload-detail',
            hidden: true,
            meta: { breadcrumb: '详情' },
            component: () => import('@/views/Cluster/Workload/detail.vue'),
          },
        ],
      },
      {
        path: '/cluster-manage/pod',
        name: 'pod',
        meta: { breadcrumb: '容器组' },
        component: () => import('@/views/Cluster/Pod/index.vue'),
        children: [
          {
            path: '/cluster-manage/pod/:name',
            name: 'pod-detail',
            hidden: true,
            meta: { breadcrumb: '详情' },
            component: () => import('@/views/Cluster/Pod/detail.vue'),
          },
        ],
      },
      {
        path: '/cluster-manage/service',
        name: 'service',
        meta: { breadcrumb: '服务与路由' },
        component: () => import('@/views/Cluster/Service/index.vue'),
        children: [
          {
            path: '/cluster-manage/service/:name',
            name: 'service-detail',
            hidden: true,
            meta: { breadcrumb: '详情' },
            component: () => import('@/views/Cluster/Service/detail.vue'),
          },
        ],
      },
    ],
  },
];

const outherRouter: ICustomRouterRaw[] = [
  {
    path: '/model',
    name: 'model',
    meta: { breadcrumb: '模型广场' },
    icon: '#icon-moxingjishi',
    children: [
      {
        path: '',
        name: 'list',
        hidden: true,
        meta: { breadcrumb: '模型列表' },
        component: () => import('@/views/Model/index.vue'),
      },
      {
        path: 'detail',
        name: 'detail',
        meta: { breadcrumb: '模型详情' },
        hidden: true,
        component: () => import('@/views/Model/Info/index.vue'),
      },
    ],
  },
  {
    path: '/model/manage',
    name: 'modelManage',
    meta: { breadcrumb: '模型广场管理' },
    icon: '#icon-moxingjishi',
    children: [
      {
        path: '',
        name: 'manage',
        hidden: true,
        meta: {
          breadcrumb: '模型列表',
        },
        component: () => import('@/views/Model/Manage/index.vue'),
      },
      {
        path: 'detail',
        name: 'manageDetail',
        meta: { breadcrumb: '模型详情' },
        hidden: true,
        component: () => import('@/views/Model/Info/index.vue'),
      },
    ],
  },
  {
    path: '/docker',
    name: 'docker',
    icon: '#icon-jxgl',
    meta: { breadcrumb: '镜像管理' },
    children: [
      {
        path: '',
        name: 'docker_official',
        hidden: true,
        meta: { breadcrumb: '官方镜像' },
        component: () => import('@/views/Docker/index.vue'),
      },
    ],
  },
  {
    path: '/label',
    name: 'label',
    icon: '#icon-moxingbiaoqian',
    meta: { breadcrumb: '模型标签' },
    children: [
      {
        path: '',
        name: 'label_manage',
        hidden: true,
        meta: { breadcrumb: '标签管理' },
        component: () => import('@/views/Label/index.vue'),
      },
    ],
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  // @ts-expect-error
  routes: [...menuRouter],
});

let isRoutesBAdded = false;

router.beforeEach((to, _, next) => {
  const { name } = to;
  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');

  if (!isRoutesBAdded && name !== 'login') {
    menuRouter.map((e) => router.removeRoute(e.name));
    let _outherRouter = outherRouter;
    // 根据情况修改路由
    const model = _outherRouter.find((e) => e.name == 'model') as ICustomRouterRaw;
    if (model) {
      if (!administratorIds.includes(userId)) {
        _outherRouter = [model];
      }
    }
    // @ts-expect-error
    [..._outherRouter, ...menuRouter].map((e) => router.addRoute(e));
    useStore().state.routers = [..._outherRouter, ...menuRouter];
    isRoutesBAdded = true; // 标记已添加

    next(to.fullPath);
    return;
  }
  next();
});

export default router;
