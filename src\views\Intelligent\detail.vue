<script setup lang="ts">
  import { ref, onMounted, KeepAlive } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { LeftOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { publishAgent, agentVersionDetail } from '@/api/agent';
  import ConfigTab from './Components/ConfigTab.vue';
  import PublishTab from './Components/PublishTab.vue';

  const router = useRouter();
  const route = useRoute();
  const updated_at = ref('');
  const currentTab = ref('config');
  const appName = ref('');
  const deployId = ref('');

  const handleBack = () => {
    router.back();
  };

  const handleAutoSaveTimeUpdate = (time: string) => {
    updated_at.value = time;
  };

  const confirmPublish = async () => {
    if (!deployId.value) {
      message.warn('模型未配置，请配置后再发布');
      return;
    }
    try {
      await publishAgent(String(route.params.id));
      message.success('已提交发布');
    } catch {}
    router.back();
  };

  const fetchBasicInfo = async () => {
    try {
      const data = await agentVersionDetail(String(route.params.id));
      appName.value = data.name;
      deployId.value = data.deploy_id;
    } catch (error) {
      console.error('获取应用信息失败：', error);
    }
  };

  onMounted(async () => {
    await fetchBasicInfo();
  });
</script>

<template>
  <div class="header text-18px relative">
    <div>
      <LeftOutlined @click="handleBack" />
      <span class="m-l-10px">{{ appName }}</span>
    </div>
    <a-radio-group v-model:value="currentTab" class="absolute left-1/2 transform -translate-x-1/2">
      <a-radio-button value="config">配置</a-radio-button>
      <a-radio-button value="publish">发布</a-radio-button>
    </a-radio-group>
    <div class="flex text-14px text-#797979">
      <div class="leading-[32px] mr-10px">自动保存于：{{ updated_at }}</div>
      <a-button type="primary" @click="confirmPublish">发布</a-button>
    </div>
  </div>
  <div class="container">
    <KeepAlive>
      <ConfigTab
        v-if="currentTab === 'config'"
        :agent-id="String(route.params.id)"
        @auto-save-time-update="handleAutoSaveTimeUpdate"
      />
      <PublishTab v-else-if="currentTab === 'publish'" />
    </KeepAlive>
  </div>
</template>

<style scoped lang="less">
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #ccc;
  }
  .container {
    height: calc(100% - 40px);
  }
</style>
