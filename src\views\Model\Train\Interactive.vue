<script setup lang="ts">
  import { ref, reactive, watch, onMounted } from 'vue';
  import { ConfigPackage } from '@/components';
  import { Tooltip, message } from 'ant-design-vue';
  import { QuestionCircleFilled } from '@ant-design/icons-vue';
  import { trainMethods, parameter, general_parameters, trainFrameworks, trainTypes } from './index';
  import { getRandomCharacters, makeK8sNameValid } from '@/utils/common';
  import { sourceEnum } from '@/utils/enum';
  import { VAceEditor } from 'vue3-ace-editor';
  import 'ace-builds/src-noconflict/theme-chrome';
  import 'ace-builds/src-noconflict/theme-monokai.js';
  import 'ace-builds/src-noconflict/mode-sh.js';
  import { dockerDropDown } from '@/api/docker';
  import type { Rule } from 'ant-design-vue/es/form/interface';
  import type {
    ITrainFormState,
    General_parameters,
    Train_parameter,
    DataItem,
    IModelFormState,
    ITrainMethodsItem,
    ITrainTypesItem,
    ITrainFrameworksItem,
  } from '@/interface/model';
  import type { IAceOptions } from '@/interface';
  import { checkTarinOutputName, checkTrainName } from '@/api/model';
  interface IProps {
    model: Partial<IModelFormState>;
  }
  const props = defineProps<IProps>();
  const trainRef = ref();

  interface IDefaultCloseUpDStatus {
    method: boolean;
    setting: boolean;
    dataset: boolean;
    output: boolean;
    resource: boolean;
    parameter: boolean;
    command: boolean;
    childform: Record<string, boolean>;
    general_parameters: boolean;
  }

  const defaultCloseUpDStatus: IDefaultCloseUpDStatus = {
    method: true,
    setting: false,
    dataset: false,
    output: false,
    resource: true,
    parameter: false,
    command: false,
    childform: reactive<Record<string, boolean>>({
      command: true,
      docker: true,
    }),
    general_parameters: false,
  };
  const upStatus = reactive({
    ...defaultCloseUpDStatus,
  });
  const selectFramework = ref('');
  const frameworks = ref<ITrainFrameworksItem[]>([]);
  const firstMethods = ref<ITrainMethodsItem[]>([]);
  const secondTypes = ref<ITrainTypesItem[]>([]);
  const formState: ITrainFormState = reactive({
    stage: 'sft',
    finetuning_type: 'lora',
    task_name: '',
    max_run_time: 0,
    max_run_time_unit: 0,
    dataset_type: 0,
    dataset: ['identity'],
    model_name: '',
    description: '',
    resource_type: 0,
    train_parameter: {
      general_parameters: {
        learning_rate: '0.00005',
        num_train_epochs: 1,
        max_grad_norm: 1,
        max_samples: 5,
        computed_type: 'bf16',
        cutoff_len: 128,
        per_device_train_batch_size: 1,
        gradient_accumulation_steps: 2,
        val_size: 0.25,
        lr_scheduler_type: 'cosine',
      },
      other_parameters: {
        logging_steps: 5,
        save_steps: 100,
        warmup_steps: 0,
        neftune_noise_alpha: 0,
        // extra_args: '{"optim": "adamw_torch"}',
        packing: false,
        neat_packing: false,
        train_on_prompt: false,
        mask_history: false,
        resize_vocab: false,
        use_llama_pro: false,
      },
      partial_parameters: {
        freeze_trainable_layers: 128,
        freeze_trainable_modules: 'all',
        galore_rank: '',
      },
      LoRA_parameters: {
        lora_rank: 8,
        lora_alpha: 16,
        lora_dropout: 0,
        // loraplus_lr_ratio: 0,
        create_new_adapter: false,
        use_rslora: false,
        use_dora: false,
        // use_pissa: false,
        lora_target: 'all',
        // additional_target: '',
      },
      RLHF_parameters: {
        pref_beta: 0.1,
        pref_ftx: 0,
        pref_loss: 'sigmoid',
        ppo_score_norm: false,
        ppo_whiten_rewards: false,
      },
      GaLore_parameters: {
        use_galore: false,
        galore_rank: 16,
        galore_update_interval: 200,
        galore_scale: 2,
        galore_target: 'all',
      },
      BAdam_parameters: {
        use_badam: false,
        badam_mode: 'layer',
        // adam_switch_mode: 'ascending',
        badam_switch_interval: 50,
        badam_update_ratio: 0.05,
      },
    },
    // run_command: '',
    docker_image_id: '',
  });
  const disableState: Record<string, boolean> = reactive({
    other_parameters: false,
    partial_parameters: false,
    LoRA_parameters: false,
    RLHF_parameters: false,
    GaLore_parameters: false,
    BAdam_parameters: false,
  });
  const parameterMap: Record<string, string> = {
    general_parameters: '通用参数设置',
    other_parameters: '其它参数设置',
    partial_parameters: '部分参数微调',
    LoRA_parameters: 'LoRA 参数设置',
    RLHF_parameters: 'RLHF 参数设置',
    GaLore_parameters: 'GaLore 参数设置',
    BAdam_parameters: 'BAdam 参数设置',
  };

  interface IState {
    dockerList: Record<string, string>[];
    docker: string;
    dockerType: number;
  }
  const state = reactive<IState>({
    dockerList: [],
    docker: 'reg.shukeyun.com:9088/algorithm/ai-agent-vllm:v2',
    dockerType: 0,
  });

  const aceOptions: IAceOptions = {
    enableBasicAutocompletion: true,
    enableSnippets: true,
    enableLiveAutocompletion: true,
    tabSize: 4,
    enableEmmet: true,
    fontSize: 15,
    showPrintMargin: false, // 去除编辑器里的竖线
    highlightActiveLine: true,
    useWorker: true,
    readOnly: false,
  };

  const resources = ref<DataItem[]>([{ key: '1', GPU: '1 * NVIDIA V100', CPU: 'Dynamic', memory: 'Dynamic' }]);
  const selectedRowKeys = ref<(string | number)[]>(['1']);
  const getCheckboxProps = (record: DataItem) => {
    return {
      disabled: record.disabled,
    };
  };
  const rowSelection = reactive({
    checkStrictly: false,
    type: 'radio',
    getCheckboxProps: getCheckboxProps,
    selectedRowKeys,
    onChange: (keys: (string | number)[], selectedRows: DataItem[]) => {
      selectedRowKeys.value = keys;
      const items: DataItem[] = JSON.parse(JSON.stringify(selectedRows));
      items.forEach((i) => {
        delete i.key;
        delete i.disabled;
      });
      resources.value = items;
    },
  });
  const resourceColumn = [
    {
      title: 'GPU',
      dataIndex: 'GPU',
      key: 'GPU',
    },
    {
      title: 'CPU',
      dataIndex: 'CPU',
      key: 'CPU',
    },
    {
      title: '内存',
      dataIndex: 'memory',
      key: 'memory',
    },
  ];

  const validateFields = () => {
    return new Promise(async (resolve) => {
      try {
        await trainRef.value.validateFields();
        resolve(true);
      } catch (e: unknown) {
        // @ts-expect-error
        message.warn(e.errorFields[0].errors);
      }
    });
  };
  // const exampleDataSource = reactive([])
  const closeUpStatus = (
    key: keyof typeof defaultCloseUpDStatus,
    notClose: (keyof typeof defaultCloseUpDStatus)[] = [],
    childKeys: string[] = [],
  ) => {
    const temp = { ...defaultCloseUpDStatus };
    if (typeof temp[key] == 'boolean') {
      // @ts-expect-error
      temp[key] = !upStatus[key];
    } else {
      //子项套多个子项
      if (childKeys)
        childKeys.map((e) => {
          if (!Object.keys(temp[key]).includes(e))
            // @ts-expect-error
            temp[key][e] = true; //赋默认值
          // @ts-expect-error
          else temp[key][e] = !temp[key][e];
        });
    }
    //忽略父项
    for (const key of notClose) {
      // @ts-expect-error
      temp[key] = true;
    }
    Object.assign(upStatus, temp);
  };

  watch(
    () => props.model,
    async (model) => {
      if (!state.dockerList.length) {
        const list = await dockerDropDown();
        // @ts-expect-error
        state.dockerList = list.filter((e) => e);
        if (list.length) formState.docker_image_id = state.dockerList[0].id;
      }
      if (model) {
        const { name, source_name, source_path, tf } = model;
        const customName = `${makeK8sNameValid(`${source_name}_${getRandomCharacters()}`)}`;
        formState.task_name = customName;
        formState.model_name = customName;
        //     formState.run_command = `docker run -d
        // --name ${formState.model_name}
        // -v ${source_path}:/ckpt/${source_name}
        // -v /data/public/dataset:/dataset${state.dockerList.length ? `\n${state.dockerList[0].name}` : ''}`;

        // 训练方式联动
        const frameworkKeys = Object.keys(tf || {});
        selectFramework.value = frameworkKeys.length ? frameworkKeys[0] : '';
        frameworks.value = trainFrameworks.filter((item) => frameworkKeys.includes(item.value));

        const methods = Object.keys(tf![selectFramework.value]);
        formState.stage = methods.length ? methods[0] : '';
        firstMethods.value = trainMethods.filter((item) => methods.includes(item.value));
        // @ts-expect-error
        const types = tf![selectFramework.value][formState.stage];
        secondTypes.value = trainTypes.filter((item) => types.includes(item.value));
        formState.finetuning_type = types.length ? types[0] : '';
      }
    },
    { deep: true, immediate: true },
  );

  // const runChang = (e: Event, key: string) => {
  //   if (key == 'docker')
  //     formState.run_command = formState.run_command.replace(
  //       /^reg.shukeyun.*$/m,
  //       // @ts-expect-error
  //       state.dockerList.find((n) => n.id == e).name,
  //     );
  //   if (key == 'model_name')
  //     formState.run_command = formState.run_command.replace(/^--name.*$/m, `--name ${formState.model_name}`);
  // };

  const validatorName = async (_rule: Rule, value: string) => {
    if (value == '' || value.trim().length == 0) {
      return Promise.reject('请输入服务名称');
    }
    const regex = /^[a-z0-9][a-z0-9\-.]*[a-z0-9]$/;
    if (!regex.test(value)) {
      return Promise.reject('格式不正确：仅支持小写字母、数字、-和.，且首尾必须为字母或数字');
    }
    if (value.length > 63) {
      return Promise.reject('任务名称最多输入 63 个字');
    }
    try {
      await checkTrainName(value);
    } catch (e) {
      if (e === 'AlreadyExists') {
        return Promise.reject('该名称已存在，请重新命名');
      }
      return Promise.reject(e);
    }
    return Promise.resolve();
  };

  const validatorModelName = async (_rule: Rule, value: string) => {
    if (value == '' || value.trim().length == 0) {
      return Promise.reject('请输入服务名称');
    }
    const regex = /^[a-z0-9][a-z0-9\-.]*[a-z0-9]$/;
    if (!regex.test(value)) {
      return Promise.reject('格式不正确：仅支持小写字母、数字、-和.，且首尾必须为字母或数字');
    }
    if (value.length > 63) {
      return Promise.reject('模型名称最多输入 63 个字');
    }
    try {
      await checkTarinOutputName(value);
    } catch (e) {
      if (e === 'AlreadyExists') {
        return Promise.reject('该名称已存在，请重新命名');
      }
      return Promise.reject(e);
    }
    return Promise.resolve();
  };

  const handleChangeDataSet = (e: number) => {
    formState.dataset = [];
    formState.dataset = e === 0 ? ['identity'] : ['travel_plan'];
  };

  const handleChangeFramework = (type: string) => {
    selectFramework.value = type;
    const methods = Object.keys(props.model.tf![type]);
    formState.stage = methods[0] || '';
    firstMethods.value = trainMethods.filter((item) => methods.includes(item.value));
    // @ts-expect-error
    const types = props.model.tf![selectFramework.value][formState.stage];
    secondTypes.value = trainTypes.filter((item) => types.includes(item.value));
    formState.finetuning_type = types[0] || '';
  };
  onMounted(async () => {});

  defineExpose({
    validateFields,
    formState,
    resources,
    disableState,
  });
</script>

<template>
  <ConfigPackage :expand="upStatus.method" :expand-click="() => closeUpStatus('method')" label="训练方式">
    <div>
      <div class="mb-10px">训练框架</div>
      <div class="types">
        <template v-for="item in frameworks" :key="item.value">
          <div
            class="types-items"
            :class="{ active: selectFramework === item.value }"
            @click="handleChangeFramework(item.value)"
          >
            <div class="flex-col">
              <div class="font-bold">{{ item.name }}</div>
              <div class="c-#7f7f7f font-size-14px">{{ item.desc }}</div>
            </div>
            <div v-if="selectFramework === item.value" class="checkbox">
              <a-checkbox :checked="true"></a-checkbox>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div>
      <div class="mb-10px">训练方法</div>
      <div class="flex-nowrap flex overflow-x-auto whitespace-nowrap overflow-scroll p-x-10px">
        <div
          v-for="item in firstMethods"
          :key="item.value"
          class="radio-button-wrappe flex p-10px"
          :class="{ actived: formState.stage === item.value }"
          @click="
            formState.stage = item.value;
            formState.finetuning_type = 'lora';
          "
        >
          <span class="mr-5px">{{ item.name }}</span>
          <Tooltip>
            <template #title>
              {{ item.desc }}
            </template>
            <QuestionCircleFilled />
          </Tooltip>
        </div>
      </div>
      <div class="types">
        <template v-for="item in secondTypes" :key="item.value">
          <div
            class="types-items"
            :class="{ active: formState.finetuning_type === item.value }"
            @click="formState.finetuning_type = item.value"
          >
            <div class="flex-col">
              <div class="font-bold">{{ item.label }}</div>
              <div class="c-#7f7f7f font-size-14px">{{ item.desc }}</div>
            </div>
            <div v-if="formState.finetuning_type === item.value" class="checkbox">
              <a-checkbox :checked="true"></a-checkbox>
            </div>
          </div>
        </template>
      </div>
    </div>
  </ConfigPackage>
  <a-form ref="trainRef" :model="formState" name="basic" autocomplete="off" layout="vertical">
    <ConfigPackage :expand="upStatus.setting" :expand-click="() => closeUpStatus('setting')" label="训练设置">
      <a-form-item label="任务名称" name="task_name" :rules="[{ required: true, validator: validatorName }]">
        <a-input v-model:value="formState.task_name" placeholder="请输入任务名称" show-count :maxlength="63" />
      </a-form-item>
      <a-form-item label="最大运行时长" name="max_run_time">
        <a-input-number
          v-model:value="formState.max_run_time"
          :precision="0"
          :min="0"
          :max="formState.max_run_time_unit === 0 ? 60 : 24"
          placeholder="请输入最大运行时长"
          :style="{ width: '100%' }"
        >
          <template #addonAfter>
            <a-select
              v-model:value="formState.max_run_time_unit"
              style="width: 100px"
              @change="formState.max_run_time = 0"
            >
              <a-select-option :value="0">分钟</a-select-option>
              <a-select-option :value="1">小时</a-select-option>
            </a-select>
          </template>
        </a-input-number>
      </a-form-item>
    </ConfigPackage>

    <ConfigPackage :expand="upStatus.dataset" :expand-click="() => closeUpStatus('dataset')" label="数据集配置">
      <a-row :style="{ marginBottom: '10px' }">
        <a-col :span="6">
          <a-select v-model:value="formState.dataset_type" style="width: 100%" @change="handleChangeDataSet">
            <a-select-option :value="0">公开数据集</a-select-option>
            <a-select-option :value="1">自研数据集</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="18">
          <div class="dataset">
            <a-form-item
              label=""
              name="dataset"
              :rules="[{ required: true, message: '请选择数据集' }]"
              :style="{ marginBottom: '0px' }"
            >
              <a-select
                v-model:value="formState.dataset"
                mode="multiple"
                style="width: 100%"
                placeholder="请选择数据集"
              >
                <a-select-option v-if="formState.dataset_type === 0" value="identity">
                  <div>身份意识微调数据集<span> identity</span></div>
                </a-select-option>
                <a-select-option v-if="formState.dataset_type === 1" value="travel_plan">
                  <div>环球数科文旅垂类数据集<span> travel_plan</span></div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </div>
        </a-col>
      </a-row>
      <!-- <div>
        <div class="mt-20px mb-10px">
          数据集示例
          <Tooltip>
            <template #title>
              数据集示例提示文案
            </template>
            <QuestionCircleFilled />
          </Tooltip>
        </div>
        <a-table :dataSource="exampleDataSource" :columns="exampleColumn" :pagination="false"
        />
      </div> -->
    </ConfigPackage>

    <ConfigPackage :expand="upStatus.output" :expand-click="() => closeUpStatus('output')" label="输出配置">
      <a-form-item label="模型名称" name="model_name" :rules="[{ required: true, validator: validatorModelName }]">
        <a-input v-model:value="formState.model_name" placeholder="请输入任务名称" show-count :maxlength="63" />
      </a-form-item>
      <a-form-item label="版本描述" name="description">
        <a-textarea v-model:value="formState.description" placeholder="请输入版本描述" :rows="4" />
      </a-form-item>
    </ConfigPackage>

    <ConfigPackage :expand="upStatus.resource" :expand-click="() => closeUpStatus('resource')" label="计算资源配置">
      <a-form :model="formState" autocomplete="off" layout="vertical">
        <ConfigPackage
          :expand="upStatus.childform['resourceList']"
          :expand-click="() => closeUpStatus('childform', ['resource'], ['resourceList'])"
          label="资源配置"
          spacious
        >
          <a-form-item label="资源列表" :rules="[{ required: true }]">
            <a-table
              :data-source="sourceEnum"
              :columns="resourceColumn"
              :row-selection="rowSelection"
              row-key="key"
              :pagination="false"
            />
          </a-form-item>
        </ConfigPackage>
        <ConfigPackage
          :expand="upStatus.childform['docker']"
          :expand-click="() => closeUpStatus('childform', ['resource'], ['docker'])"
          label="镜像配置"
          spacious
        >
          <a-form-item label="镜像类型">
            <a-select v-model:value="state.dockerType" style="width: 100%">
              <a-select-option :value="0">官方镜像</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="镜像选择" name="docker_image_id" :rules="[{ required: true, message: '请选择镜像' }]">
            <a-select v-model:value="formState.docker_image_id" style="width: 100%">
              <a-select-option v-for="n in state.dockerList" :key="n.id" :value="n.id">{{ n.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </ConfigPackage>
        <!-- <ConfigPackage
          :expand="upStatus.childform['command']"
          :expand-click="() => closeUpStatus('childform', ['resource'], ['command'])"
          label="初始命令"
          tip="与容器相关的初始命令"
          spacious
        >
          <v-ace-editor
            v-model:value="formState.run_command"
            lang="sh"
            theme="monokai"
            :options="aceOptions"
            style="height: 300px; border: 1px solid #dbd3d3"
          />
        </ConfigPackage> -->
      </a-form>
    </ConfigPackage>

    <ConfigPackage :expand-click="() => closeUpStatus('parameter')" :expand="upStatus.parameter" label="超参数配置">
      <ConfigPackage
        :expand="upStatus.childform['general_parameters']"
        :expand-click="() => closeUpStatus('childform', ['parameter'], ['general_parameters'])"
        label="通用参数设置"
      >
        <div>
          <div class="parameter">
            <div v-for="item in general_parameters" :key="item.value" class="parameter-items">
              <a-form-item
                :name="['train_parameter', 'general_parameters', item.value]"
                :rules="[
                  {
                    required: item.required,
                    message: ['input', 'inputNumber'].includes(item.type) ? '请输入' : '请选择',
                  },
                ]"
              >
                <template #label>
                  <span>{{ item.label }}</span>
                  <Tooltip v-if="item.desc">
                    <template #title>{{ item.desc }}</template>
                    <QuestionCircleFilled class="ml-5px" />
                  </Tooltip>
                </template>
                <a-input
                  v-if="item.type === 'input'"
                  v-model:value="
                    formState.train_parameter.general_parameters[`${item.value as keyof General_parameters}`]
                  "
                  placeholder="请输入"
                />
                <a-input-number
                  v-if="item.type === 'inputNumber'"
                  v-model:value="
                    formState.train_parameter.general_parameters[`${item.value as keyof General_parameters}`]
                  "
                  :min="item.min"
                  :max="item.max"
                  :step="item.step || 1"
                  :precision="item.precision || 0"
                  :style="{ width: '100%' }"
                  placeholder="请输入"
                ></a-input-number>
                <a-select
                  v-if="item.type === 'select'"
                  v-model:value="
                    formState.train_parameter.general_parameters[`${item.value as keyof General_parameters}`]
                  "
                  style="width: 100%"
                >
                  <a-select-option v-for="opt in item.option" :key="opt.value" :value="opt.value">{{
                    opt.label
                  }}</a-select-option>
                </a-select>
                <a-checkbox
                  v-if="item.type === 'checkbox'"
                  v-model:checked="
                    formState.train_parameter.general_parameters[`${item.value as keyof General_parameters}`]
                  "
                ></a-checkbox>
              </a-form-item>
            </div>
          </div>
        </div>
      </ConfigPackage>
      <ConfigPackage
        v-for="key in Object.keys(parameter)"
        :key="key"
        v-model:on="disableState[key]"
        :expand="upStatus.childform[key]"
        :expand-click="() => closeUpStatus('childform', ['parameter'], [key])"
        :label="parameterMap[key]"
      >
        <div>
          <div class="parameter">
            <div v-for="item in parameter[key]" :key="item.value" class="parameter-items">
              <a-form-item
                :name="['train_parameter', key, item.value]"
                :rules="[
                  {
                    required: item.required,
                    message: ['input', 'inputNumber'].includes(item.type) ? '请输入' : '请选择',
                  },
                ]"
              >
                <template #label>
                  <span>{{ item.label }}</span>
                  <Tooltip v-if="item.desc">
                    <template #title>{{ item.desc }}</template>
                    <QuestionCircleFilled class="ml-5px" />
                  </Tooltip>
                </template>
                <a-input
                  v-if="item.type === 'input'"
                  v-model:value="
                    formState.train_parameter[key as keyof Train_parameter][`${item.value}` as keyof General_parameters]
                  "
                  placeholder="请输入"
                />
                <a-input-number
                  v-if="item.type === 'inputNumber'"
                  v-model:value="formState.train_parameter[key][`${item.value}`]"
                  :min="item.min"
                  :max="item.max"
                  :step="item.step || 1"
                  :precision="item.precision || 0"
                  :style="{ width: '100%' }"
                  placeholder="请输入"
                ></a-input-number>
                <a-select
                  v-if="item.type === 'select'"
                  v-model:value="formState.train_parameter[key][`${item.value}`]"
                  style="width: 100%"
                >
                  <a-select-option v-for="opt in item.option" :key="opt.value" :value="opt.value">{{
                    opt.label
                  }}</a-select-option>
                </a-select>
                <a-checkbox
                  v-if="item.type === 'checkbox'"
                  v-model:checked="formState.train_parameter[key][`${item.value}`]"
                ></a-checkbox>
              </a-form-item>
            </div>
          </div>
        </div>
      </ConfigPackage>
    </ConfigPackage>
  </a-form>
</template>

<style scoped lang="less">
  @import url('./index.less');

  .parameter {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: space-between;
    padding: 10px;

    .parameter-items {
      flex: 1 1 49%;
      max-width: 49%;
    }
  }

  .dataset {
    :deep(.ant-form-item) {
      margin-bottom: 0 !important;
    }
  }
</style>
