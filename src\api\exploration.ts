import type { IServiceList, IPostServiceSetting, ISendMessageProps, IUpdateDialogTitleProps, IOCRByImage, IOCRByUrl } from '@/interface/exploration';
import { getEnv } from '@/utils/getEnv';
import request from '@/utils/request';

const { VITE_DATAFOX_BASE_URL } = getEnv();

export const fetchServiceList = (data: { exp_temp: string, published_id?: string }) => {
  const { exp_temp, published_id } = data
  return request.$Axios.get(`/deploy/${exp_temp}/filter_list/by_manager`, { published_id });
}
export const fetchServiceListByUser = (data: { exp_temp: string }) => {
  const { exp_temp } = data
  return request.$Axios.get(`/deploy/${exp_temp}/filter_list/by_user`);
}
export const fetchServiceListByModelId = (modelId: string) => {
  return request.$Axios.get(`/deploy/list/by_model_id?model_id=${modelId}`);
}


// 查询服务设置
export const getServiceSetting = (id: string) => {
  return request.$Axios.get(`/deploy/config/${id}`,);
}

// 保存服务设置
export const postServiceSetting = (data: IPostServiceSetting) => {
  return request.$Axios.post(`/deploy/config`, data);
}

// 发送消息
export const sendMessage = (data: ISendMessageProps) => {
  return request.$Axios.post(`/exp_center/dialog/chat`, data);
}

// 修改对话名称
export const updateDialogTitle = (data: IUpdateDialogTitleProps) => {
  return request.$Axios.put(`/exp_center/dialog/title`, data);
}
// 获取当前服务的历史记录
export const fetchHistoryList = (deploy_id: string) => {
  return request.$Axios.get(`/exp_center/dialog/list`, { deploy_id });
}
// 获取当前会话列表
export const fetchDialogList = (dialog_id: string) => {
  return request.$Axios.get(`/exp_center/dialog/chat/list`, { dialog_id });
}
// 获取文本对话模型
export const fetchTextChartModelList = () => {
  return request.$Axios.get(`/exp_center/text_chat/model_list`);
}
// 获取OCR识别模型
export const fetchOCRModelList = () => {
  return request.$Axios.get(`/exp_center/ocr/model_list`);
}
// OCR 识别
export const OCRByImage = (data: IOCRByImage) => {
  const { ocr_category, deploy_id, image } = data
  return request.$Axios.post(`/exp_center/ocr/${ocr_category}/image_file/${deploy_id}`, image);
}
// OCR 通过url识别
export const OCRByUrl = (data: IOCRByUrl) => {
  const { ocr_category, deploy_id, url } = data
  return request.$Axios.post(`/exp_center/ocr/${ocr_category}/image_url/${deploy_id}`, { url });
}
// 上传文件到minio
export const uploadImages = (files: FormData) => {
  return request.$Axios.post(`/minio/list/[${VITE_DATAFOX_BASE_URL}]`, files);
};