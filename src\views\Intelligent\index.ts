// 编辑和发布失败都状态都显示已发布，区别是要加红点
export const STATUS_OPTIONS: { label: string; value: string; color: string }[] = [
  { label: '已发布', value: 'archived', color: 'success' },
  { label: '已发布', value: 'draft', color: 'success' }, // 编辑
  { label: '已发布', value: 'published_failed', color: 'success' }, // 发布失败
  { label: '未发布', value: 'new', color: 'warning' },
]

export const example = `
"""你现在是一位专业的旅游行程规划师。请基于以下历史对话内容，为用户制定合理的旅游行程计划：

  ---历史对话记录---"""

      + history_json

      + """

  ---对话记录结束---

""" + edit_msg + """

  请遵循以下规则制定行程:

  1. 每天必须安排两个景点，分别在上午和下午

  2. 如果有可选景点列表，可以参考列表中的景点，但行程不必完全按照列表中的景点进行规划

  3. 必须考虑景点之间的交通时间，确保行程安排合理可行

  4. 考虑景点的游览时间，不要安排过于紧凑的行程

  5. 行程时间仅按照用户所提供的"游玩时间"来制定行程，若游玩时间为1天，则仅安排一天的行程

  6. 根据景点的类型、位置和主题合理分配，使行程体验更加连贯和丰富

  7. 行程中不允许出现重复的景点



  你的回复必须严格按照以下JSON格式返回，不要添加任何额外文本或解释:

[

  {"title": "第一天上午", "msg": "第一天上午旅游的具体内容和安排", "reason": "景点的推荐理由"},

  {"title": "第一天下午", "msg": "第一天下午旅游的具体内容和安排", "reason": "景点的推荐理由"},

  {"title": "第二天上午", "msg": "第二天上午旅游的具体内容和安排", "reason": "景点的推荐理由"},

  ...以此类推直到最后一天

]



  在"msg"字段中，请提供以下详细信息:

  1. 景点名称

  2. 景点简介和推荐理由（100字以内）

  3. 该景点的主要看点或活动（2-3个）

  4. 注意使用冒号(:)分隔景点名称和简介

  5. 景点简介和主要看点使用换行符分隔



  在"reason"字段中，请提供以下详细信息:

  1. 景点的推荐理由，可以从景点特色（独特的自然风光或人文景观、历史文化背景和故事、标志性建筑或必看亮点）进行推荐，不要超过200字。



  对于可选景点列表：

  1. 选取其中最适合行程的景点，确保整体体验最佳

  2. 如果某些推荐景点不合适（如位置太远、与其他景点冲突等），可以不纳入行程



 

  请确保行程规划合理，考虑交通时间，并且符合用户在对话中表达的偏好。只使用中文回答

  """

  )`;

export const templateStr = `# 角色：输入角色名称

  角色概述和主要职责的一句话描述
  
  ## 目标：
  
  角色的工作目标，如果有多目标可以分点列出，但建议更聚焦1-2个目标"
  
  
  
  ## 技能：
  
  1.  为了实现目标，角色需要具备的技能1
  
  2. 为了实现目标，角色需要具备的技能2
  
  3. 为了实现目标，角色需要具备的技能3
  
  
  
  ## 输出格式：
  
  如果对角色的输出格式有特定要求，可以在这里强调并举例说明想要的输出格式
  
  
  
  ## 限制：
  
  描述角色在互动过程中需要遵循的限制条件1
  
  描述角色在互动过程中需要遵循的限制条件2
  
  描述角色在互动过程中需要遵循的限制条件3`

export const DEFAULT_CALLPARAMS = {
  max_tokens: 512,
  temperature: 0.7,
  top_p: 1,
  repetition_penalty: 1.2,
} 