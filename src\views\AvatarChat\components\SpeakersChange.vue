<script setup lang="ts">
  import { ref, reactive, onBeforeUnmount } from 'vue';
  import stopIcon from '@/assets/image/base/pictures/stop.png';
  import playIcon from '@/assets/image/base/pictures/play.png';

  interface SpeakersProps {
    isCollapsed: boolean;

    speakersData: any;
  }

  // Props
  const props = defineProps<SpeakersProps>();
  const tagsData = reactive(['普通话', '四川话', '外语', '粤语']);
  // 映射标签到数据键
  const tagToKeyMap = {
    普通话: 'mandarin',
    四川话: 'sichuan',
    外语: 'foreign',
    粤语: 'cantonese',
  };

  // 当前选中的语言类型
  const selectedLanguage = ref('mandarin');
  const selectedSpeaker = ref<string | null>(null);
  const hoverStates = reactive<Record<number, boolean>>({});
  const selectedTag = ref<string | null>('普通话');
  const isPlaying = reactive<Record<number, boolean>>({});

  // 管理音频元素
  const audioElements = reactive<Record<number, HTMLAudioElement | null>>({});

  const handleChange = (tag: string) => {
    selectedTag.value = tag; // 设置当前选中的标签
    // 获取对应的数据键
    const key = tagToKeyMap[tag as keyof typeof tagToKeyMap];
    if (key) {
      selectedLanguage.value = key;

      selectedSpeaker.value = null; // 清空选中的说话人
      // 清空所有播放状态和音频元素
      Object.keys(isPlaying).forEach((index) => {
        isPlaying[Number(index)] = false;
      });
      Object.values(audioElements).forEach((audio) => {
        audio?.pause();
        audio?.remove();
      });
      Object.keys(audioElements).forEach((index) => {
        audioElements[Number(index)] = null;
      });
    } else {
      console.warn(`未找到与标签 "${tag}" 对应的语言类型`);
    }
  };

  // 播放音频
  const playAudio = (index: number, sampleFileUrl: string) => {
    if (!audioElements[index]) {
      audioElements[index] = new Audio(sampleFileUrl);
      // 添加 ended 事件监听器
      audioElements[index]!.addEventListener('ended', () => {
        selectedSpeaker.value = null; // 清除选中的说话人状态
        isPlaying[index] = false; // 停止播放状态
      });
    }

    const audio = audioElements[index];
    if (audio) {
      if (audio.paused) {
        audio
          .play()
          .then(() => {
            isPlaying[index] = true;
          })
          .catch((err) => {
            console.error('音频播放失败:', err);
          });
        // isPlaying[index] = true;
      } else {
        audio.pause();
        isPlaying[index] = false;
      }
    }
  };

  const emit = defineEmits(['speaker-selected']);
  const handleSpeakerSelect = (name: string) => {
    selectedSpeaker.value = name;
    emit('speaker-selected', name);
  };

  // 组件卸载时销毁音频元素
  onBeforeUnmount(() => {
    Object.values(audioElements).forEach((audio) => {
      audio?.pause();
      audio?.remove();
    });
  });
</script>

<template>
  <div class="speakers-container" :style="{ overflow: props.isCollapsed ? 'hidden' : '324px' }">
    <div class="speakers-tag">
      <a-tag
        v-for="tag in tagsData"
        :key="tag"
        :class="{ selected: selectedTag === tag }"
        @click="() => handleChange(tag)"
      >
        {{ tag }}
      </a-tag>
    </div>
    <div class="speakers-container-image">
      <div
        v-for="(item, index) in props.speakersData[selectedLanguage]"
        :key="index"
        class="imageBox"
        :class="{ selected: selectedSpeaker === item.name }"
        @click="handleSpeakerSelect(item.name)"
        @mouseenter="hoverStates[index] = true"
        @mouseleave="hoverStates[index] = false"
      >
        <div class="img-container" :class="{ grayscale: hoverStates[index] || isPlaying[index] }">
          <img class="img" :src="item.image_url" :alt="`${item.name} ${index}`" />
          <!-- 仅在 hover 状态或音频播放时显示图标图层 -->
          <div v-if="hoverStates[index] || isPlaying[index]" class="icon-overlay">
            <img v-if="!isPlaying[index]" :src="playIcon" alt="播放" @click="playAudio(index, item.sample_file_url)" />
            <img v-else :src="stopIcon" alt="停止" @click="playAudio(index, item.sample_file_url)" />
          </div>
        </div>
        <!-- <img class="img" :src="item.image_url" :alt="`${item.name} ${index}`" /> -->
        <div class="name">{{ item?.description || item?.name }}</div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
  .speakers-container {
    width: 100%;
    height: 100%;
    background: #ffffff;

    .speakers-tag {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-around;
      height: 32px;
      margin: 10px 20px;

      :deep(.ant-tag) {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        padding: 7px 14px;
        background: #ffffff;

        font-size: 14px;
        color: #969799;
        line-height: 20px;
        text-align: right;
        border: none;
        &:hover {
          color: #000000;
        }
        &.selected {
          background: #f2f8ff;
          color: #000000;
        }
      }
    }

    .speakers-container-image {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      max-height: 685px; /* 设置固定高度 */
      overflow-y: auto; /* 启用纵向滚动 */
      overflow-x: hidden; /* 禁用横向滚动 */
      background: #ffffff;
      box-shadow: 4px 2px 24px 0px rgba(123, 123, 123, 0.1);

      /* 隐藏滚动条 */
      ::-webkit-scrollbar {
        display: none; /* 隐藏滚动条 */
      }

      -ms-overflow-style: none; /* 适用于 IE 和 Edge */
      scrollbar-width: none; /* 适用于 Firefox */

      .imageBox {
        width: 134px;
        height: 128px;
        cursor: pointer;
        overflow: hidden;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        margin: 10px;
        background: #fafafa;
        /* box-shadow: 4px 2px 24px 0px rgba(123, 123, 123, 0.1); */

        .img {
          width: 60px;
          height: 60px;
          /* margin: 2px 0px; */
          /* border-radius: 8px; */
        }

        &:hover {
          border: 1px solid #1777ff;
        }

        &.selected {
          border: 2px solid #1777ff;
        }

        .img-container {
          position: relative;
        }

        .grayscale {
          filter: grayscale(100%);
        }

        .icon-overlay {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .icon-overlay img {
          width: 30px;
          height: 30px;
        }

        .play-container {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 60px;
        }
      }
    }
  }
</style>
