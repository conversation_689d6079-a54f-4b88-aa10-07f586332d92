<script setup lang="ts">
  import { defineProps, watch, nextTick, ref } from 'vue';
  import { DownOutlined } from '@ant-design/icons-vue';

  interface DetailItem {
    type: string;
    text: string;
  }

  const props = defineProps<{
    openModal: boolean;
    detailData: DetailItem[];
  }>();

  const emit = defineEmits(['close', ' update:detailData']);

  const detailMsgBoxRef = ref<any>(undefined);

  const handleCloseDetail = () => {
    console.log('收起');
    emit('close');
  };

  watch(props.detailData, () => {
    nextTick(() => {
      (detailMsgBoxRef.value as HTMLElement).scrollTo({ left: 0, top: detailMsgBoxRef.value.scrollHeight });
    });
  });
</script>
<template>
  <div v-if="openModal" class="details-modal">
    <!-- 收起按钮 -->
    <a-button class="modal-btn" @click="handleCloseDetail">
      <template #icon>
        <DownOutlined />
      </template>
      收起
    </a-button>

    <!-- 详情列表 -->
    <div ref="detailMsgBoxRef" class="details-msg-box">
      <template v-for="(item, i) in props.detailData" :key="i">
        <!-- 问题展示 -->
        <div v-if="item.type === 'question'" class="answer-box">
          <span class="text">{{ item.text }}</span>
        </div>
        <!-- 回答展示 -->
        <div v-else-if="item.type === 'answer'" class="question-box">
          <span class="textRight">
            {{ item.text }}
          </span>
        </div>
      </template>
    </div>
  </div>
</template>
<style lang="less" scoped>
  .details-modal {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    background: rgba(220, 224, 230, 0.45);
    border-radius: 0px 8px 8px 8px;
    backdrop-filter: blur(4px);

    .modal-btn {
      width: 76px;
      height: 38px;
      background: rgba(220, 224, 230, 0.45);
      border-radius: 8px 8px 0px 0px;
      backdrop-filter: blur(4px);
      border: none;
      margin-top: -38px;

      &:hover {
        background: rgba(220, 224, 230, 0.45);
        cursor: default;
        color: #17181a;
      }
    }

    .details-msg-box {
      width: 100%;
      padding: 10px 0;
      height: 346px;
      padding: 25px 25px 0 25px;
      overflow-y: auto;

      .question-box {
        width: 100%;
        display: flex;
        text-align: start;
        margin-bottom: 10px;

        > span {
          max-width: 90%;
          padding: 10px 15px;
          display: inline-block;
          background: #ffffff;
          border-radius: 12px 12px 12px 0px;
          font-size: 16px;
          font-weight: 400;
          font-family:
            PingFangSC,
            PingFang SC;
          color: #17181a;
          line-height: 24px;
          text-align: left;
          font-style: normal;
        }
      }

      .answer-box {
        margin: 10px 0;
        width: 100%;
        display: flex;
        flex-direction: row-reverse;
        text-align: start;

        > span {
          max-width: 90%;
          padding: 10px 15px;
          display: inline-block;
          background: #dceefd;
          border-radius: 12px 12px 0px 12px;
          border: 1px solid #ffffff;
          opacity: 0.9;
          font-size: 16px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          color: #17181a;
          line-height: 24px;
          text-align: justify;
          font-style: normal;
        }
      }

      // /* 滚动条整体部分 */
      &::-webkit-scrollbar {
        height: 6px; /* 滚动条高度 */
        width: 6px; /* 滚动条宽度 */
      }
      // /* 滚动条滑块 */
      &::-webkit-scrollbar-thumb {
        background-color: transparent; /* 滑块颜色 */
        border-radius: 10px;
      }
      &:hover::-webkit-scrollbar-thumb {
        background-color: #888; /* 滑块颜色 */
      }
    }
  }
</style>
