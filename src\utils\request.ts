import { message } from 'ant-design-vue';
import axios, { type AxiosRequestConfig, type AxiosResponse } from 'axios';
import qs from 'qs';
import { getLocalItem, getRandomCharacters, goToLogin } from './common';
import { JumpToCasLogin } from './jumpToCas';
import { getEnv } from '@/utils';
const { VITE_APP_BASE_URL } = getEnv();

class Request {
  static CONFIG = {
    repeat_request_cancel: false, // 是否开启取消重复请求, 默认为 true
    loading: false, // 是否开启loading层效果, 默认为false
    reduct_data_format: true, // 是否开启简洁的数据结构响应, 默认为true
    error_message_show: true, // 是否开启接口错误信息展示,默认为true
    code_message_show: false, // 是否开启code不为0时的信息提示, 默认为false
  };
  t = 0;
  axios = axios.create();
  requestMap = new Map(); // 网络请求的map集合
  $Axios = {
    // @ts-expect-error
    get: (url: string, params?): Promise => {
      return new Promise(async (resolve, reject) => {
        try {
          const result = await this.axios({
            url,
            method: 'get',
            params,
            paramsSerializer: function (params) {
              return qs.stringify(params, { arrayFormat: 'repeat' });
            },
          });
          resolve(result);
        } catch (err) {
          // @ts-expect-error
          if (err.message) {
            // @ts-expect-error
            reject(err.message);
          } else {
            reject(err);
          }
        }
      });
    },
    // @ts-expect-error
    post: (url: string, data?, config = {}): Promise => {
      return new Promise(async (resolve, reject) => {
        try {
          const result = await this.axios.post(url, data, config);
          resolve(result);
        } catch (err) {
          // @ts-expect-error
          if (err.message) {
            // @ts-expect-error
            reject(err.message);
          } else {
            reject(err);
          }
        }
      });
    },
    // @ts-expect-error
    put: (url: string, data?) => {
      return new Promise(async (resolve, reject) => {
        try {
          const result = await this.axios.put(url, data);
          resolve(result);
        } catch (err) {
          // @ts-expect-error
          if (err.message) {
            // @ts-expect-error
            reject(err.message);
          } else {
            reject(err);
          }
        }
      });
    },
    // @ts-expect-error
    del: (url: string, data?) => {
      // console.log('ddel', url, data);
      return new Promise(async (resolve, reject) => {
        try {
          const result = await this.axios.delete(url, { data });
          resolve(result);
        } catch (err) {
          // @ts-expect-error
          if (err.message) {
            // @ts-expect-error
            reject(err.message);
          } else {
            reject(err);
          }
        }
      });
    },
  };
  constructor() {
    this.AxiosInit();
  }
  AxiosInit() {
    this.axios.defaults.baseURL = VITE_APP_BASE_URL;
    this.axios.defaults.timeout = 125000;
    this.axios.defaults.headers.post['Content-Type'] = 'application/json;charset=UTF-8';
    // this.axios.defaults.withCredentials = true;
    this.axios.interceptors.request.use(
      (config) => {
        // 复用之前的接口，baseURL需要调整
        if (['auth/detail/', 'auth/cookies/'].includes(config.url as string)) {
          config.baseURL = 'data/api/';
        }
        if (config.url?.includes('/minio/list/')) {
          config.headers!['Content-Type'] = 'multipart/form-data';
        }
        this.handleRequest(config);
        config.headers['X-Trace-ID'] = `${crypto.randomUUID().replace(/-/g, '')}`;
        config.headers['Request_id'] = `${getRandomCharacters(16)}${Date.now()}`;
        config.headers['Authorization'] = getLocalItem('HQSK_AI_PLATFORM_FRONTEND_TOKEN');
        return config;
      },
      (error) => Promise.reject(error),
    );
    this.axios.interceptors.response.use(
      (res: AxiosResponse) => {
        this.cancelRequest(res.config);
        // console.log(res, 'config');

        // console.log('?res---', res.data, res.status);
        const data = res.data; // { code, data, msg }
        const status = Math.floor(data.code / 10000);
        const text = data.msg || res.statusText || 'Unknown error';
        if (status === 2 || status === 0) {
          return Request.CONFIG.reduct_data_format ? data.data : res;
        } else {
          const errorMsg: Record<number, string> = {
            40000: '请求错误',
            40001: '权限不正确',
            40003: '访问被限制',
            40004: '记录不存在',
            40022: '参数错误',
            40023: '服务不可用',
            40024: '预览失败',
            40100: '未登录',
            40200: '数据已存在',
            50000: '服务器错误',
          };
          // 错误信息太长截断
          // const msg = data.msg.length > 80 ? `${data.msg.slice(0, 80)}...` : data.msg;
          if (!res.config.url?.includes('/check')) {
            data.code === 50000
              ? message.error(`服务异常，请稍后再试`)
              : message.warn(`${errorMsg[data.code] || '未知错误'}`);
          }
          if ([40001, 40100].includes(data.code)) {
            // 登录失效，跳转cas
            localStorage.clear();
            JumpToCasLogin();
          }
          return Promise.reject(new Error(text));
        }
      },
      (error: AxiosResponse) => {
        this.cancelRequest(error.config);
        if (Request.CONFIG.error_message_show) {
          const errorMsg = this.handleResponseError(error.request);
          errorMsg && message.error(errorMsg);
          return Promise.reject(errorMsg);
        }
        return Promise.reject(error);
      },
    );
  }

  // 请求前的处理
  handleRequest(config: AxiosRequestConfig) {
    const { url } = config;
    const [, originalUrl, key] = url!.match(/(.*(?=\[.*\]))\[(.*)\](.*)/) || [, url, ,];
    if (key) {
      this.axios.defaults.baseURL = key;
      config.baseURL = key as string;
      config.url = originalUrl as string;
    } else {
      this.axios.defaults.baseURL = VITE_APP_BASE_URL;
      config.baseURL = VITE_APP_BASE_URL;
    }
    if (Request.CONFIG.repeat_request_cancel) {
      // 如果开启了取消配置的话，先取消掉先前的同一条请求，在添加新的请求记录
      this.cancelRequest(config);
      this.addRequest(config);
    } else {
      this.addRequest(config);
    }
  }
  // 取消重复请求
  cancelRequest(config: AxiosRequestConfig) {
    const key = this.getRequestKey(config);
    if (Request.CONFIG.repeat_request_cancel && this.requestMap.has(key)) {
      // 判断是否开启了取消配置，并且map集合中确实存在该条请求的取消函数
      const cancelToken = this.requestMap.get(key);
      cancelToken();
      this.requestMap.delete(key);
    }
  }
  // 添加一条请求记录
  addRequest(config: AxiosRequestConfig) {
    const key = this.getRequestKey(config);
    config.cancelToken =
      config.cancelToken ||
      new axios.CancelToken((c) => {
        // 给某条请求添加请求取消函数
        if (!this.requestMap.has(key)) {
          this.requestMap.set(key, c);
        }
      });
  }
  // 根据请求配置，生成一条唯一的key值
  getRequestKey(config: AxiosRequestConfig) {
    let { url, method, params, data } = config;
    if (typeof data === 'string') {
      data = JSON.parse(data);
    }
    return [url, method, JSON.stringify(params), JSON.stringify(data)].join('&');
  }
  // 统一处理响应错误
  handleResponseError(err: AxiosResponse) {
    // if (axios.isCancel(err)) return console.error('请求的重复请求：' + error.message);
    let message = '';
    if (err) {
      switch (err.status) {
        case 302:
          message = '接口重定向了！';
          break;
        case 400:
          // @ts-expect-error
          let errMsg = err.response;
          // @ts-expect-error
          switch (err.response) {
            case '{"id":["Content is conflict."]}':
              errMsg = '参数不正确：代号重复';
              break;
            case '{"detail":"duplicate pk"}':
              errMsg = '参数不正确：后缀重复';
              break;
            case '{"id":["Not found."]}':
              errMsg = '参数不正确：代号不存在';
              break;
            case '{"id":["You do not have permission to perform this action."]}':
              errMsg = '您没有权限操作';
              break;
          }
          message = errMsg;
          break;
        case 401:
          goToLogin();
          message = '您未登录，或者登录已经超时，请先登录！';
          break;
        case 403:
          message = '您没有权限操作！';
          break;
        case 404:
          message = `请求地址出错: ${err.config.url}`;
          break; // 在正确域名下
        case 406:
          message = '';
          break;
        case 408:
          message = '请求超时！';
          break;
        case 409:
          message = '系统已存在相同数据！';
          break;
        case 500:
          message = '服务器内部错误！';
          break;
        case 501:
          message = '服务未实现！';
          break;
        case 502:
          message = '网关错误！';
          break;
        case 503:
          message = '服务不可用！';
          break;
        case 504:
          message = '服务暂时无法访问，请稍后再试！';
          break;
        case 505:
          message = 'HTTP版本不受支持！';
          break;
        default:
          message = '异常问题，请联系管理员！';
          break;
      }
    }
    return message;
  }
}

export default new Request();
