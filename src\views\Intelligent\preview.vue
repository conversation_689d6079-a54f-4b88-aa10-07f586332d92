<script setup lang="ts">
  import { agentVersionDetail, getAgentDetail } from '@/api/agent';
  import type { IAgentItem, IMessage } from '@/interface/agent';
  import { ref, onMounted, reactive, onUnmounted, computed, nextTick } from 'vue';
  import { useRoute } from 'vue-router';
  import { RightCircleOutlined, StopOutlined } from '@ant-design/icons-vue';
  import { fetchEventSource } from '@microsoft/fetch-event-source';
  import sendBtn from '@/assets/image/base/pictures/sendbtn.png';
  import sendDisableBtn from '@/assets/image/base/pictures/sendDisable.png';
  import { getLocalItem } from '@/utils/common';
  import MarkdownIt from 'markdown-it';
  import type { ColumnType } from 'ant-design-vue/es/table';
  import { message } from 'ant-design-vue';
  const md = new MarkdownIt({
    html: true, // 允许HTML标签
    linkify: true, // 自动转换URL为链接
    typographer: true, // 美化排版
  });

  // const md = new MarkdownIt();
  const route = useRoute();
  interface IBasicState {
    name: string;
    logo: string;
    suggestedQuestions: string[];
    prologue: string;
    background: string;
    deploy_id: string;
  }
  const basicState = reactive<IBasicState>({
    name: '',
    logo: '',
    suggestedQuestions: [],
    prologue: '',
    background: '',
    deploy_id: '',
  });
  const inputMessage = ref('');
  interface ICitationState {
    type: string;
    visible: boolean;
    content: string | [];
  }
  const citationsState = reactive<ICitationState>({
    visible: false,
    content: [],
    type: 'string',
  });

  // 用于控制流式请求
  const abortController = ref<AbortController | null>(null);
  const spinning = ref(false);
  const isLoading = ref(false);
  const open = ref(false);
  const deployOpen = ref(false);
  const messageRef = ref();
  const containerRef = ref();
  const typingInterval = ref();
  const columns = ref<ColumnType[]>([]);
  // const citations = ref([]);
  const isDebug = computed(() => route.name === 'intelligent-detail');
  const sendParams = reactive({
    instruction: '',
    knowledge_db_ids: [],
    memory_vars: [],
  });
  // 聊天消息数据
  const messages = reactive<IMessage[]>([]);

  const handleChange = () => {
    if (inputMessage.value.trim()) {
      open.value = false;
      deployOpen.value = false;
    }
  };
  // 发送消息
  const handleSend = async (value: string) => {
    if (!value || !value.trim()) {
      open.value = true;
      return;
    }
    if (!basicState.deploy_id) {
      deployOpen.value = true;
      return;
    }
    const userMessage = { role: 'user', content: value };
    messages.push(userMessage);
    inputMessage.value = '';

    await nextTick(() => {
      if (messageRef.value) {
        messageRef.value[messageRef.value.length - 1].scrollIntoView({ behavior: 'auto', block: 'end' });
      }
    });
    await fetchAIResponse(value);
  };

  // 获取AI流式响应
  const fetchAIResponse = async (value: string) => {
    isLoading.value = true;
    abortController.value = new AbortController();
    const assistantMessage = { role: 'assistant', content: '', citations: [] };
    messages.push(assistantMessage);
    let displayedText = '';
    try {
      await fetchEventSource(
        isDebug.value
          ? `/data/ai-platform-backend/api/v1/agent/debug/chat/${route.params.id}`
          : `/data/ai-platform-backend/api/v1/agent/chat/${route.params.id}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: getLocalItem('HQSK_AI_PLATFORM_FRONTEND_TOKEN'),
          },
          body: isDebug.value
            ? JSON.stringify({
                ...sendParams,
                history: messages.slice(0, -2).map((item) => ({ role: item.role, content: item.content })),
                question: value,
              })
            : JSON.stringify({
                question: value,
                history: messages.slice(0, -2).map((item) => ({ role: item.role, content: item.content })),
              }),
          signal: abortController.value.signal,
          // @ts-expect-error
          onopen(response: Response): Promise<void> | void {
            if (!response.ok) {
              throw new Error(`Server returned ${response.status}`);
            }
          },
          onmessage(event: { data: string }): void {
            if (event.data === '[DONE]') return;
            try {
              const content: string = event.data;
              displayedText += content;
            } catch (err) {
              console.error('Error parsing message:', err);
              throw err;
            }
          },
          onclose(): void {
            // isLoading.value = false;
            abortController.value = null;
          },
          onerror(err: Error): void {
            console.log('🚀 ~ onerror ~ err:', err);
            if (err.name === 'AbortError') {
              console.error('Stream error:', err);
              messages.push({
                role: 'assistant',
                content: '发生错误: ' + err.message,
              });
            }
            isLoading.value = false;
            abortController.value = null;
          },
        },
      );
    } catch (err: unknown) {
      if (err instanceof Error && err.name !== 'AbortError') {
        console.error('Request error:', err);
        messages.push({
          role: 'assistant',
          content: '请求错误: ' + err.message,
        });
      }
      isLoading.value = false;
      abortController.value = null;
    } finally {
      const regex = /<citation>(.*?)<\/citation>/g;
      const match = regex.exec(displayedText);
      if (match !== null && match[1]) {
        messages[messages.length - 1].citations = JSON.parse(match[1]);
      }
      const answer = md.render(displayedText.replace(/<citation>.*?<\/citation>/g, ''));
      let index = 0;
      async function typeEffect() {
        if (index < answer.length) {
          messages[messages.length - 1].content += answer[index];
          // 将当前字符添加到元素中
          index++;
          // 设置下一次调用的时间间隔
          typingInterval.value = setTimeout(typeEffect, 50); // 每50毫秒打一个字
          await nextTick(() => {
            if (containerRef.value) {
              containerRef.value.scrollIntoView({ behavior: 'auto', block: 'end' });
            }
          });
        } else {
          isLoading.value = false;
        }
      }
      typeEffect();
    }
  };

  const handleClickCitation = (content: string | Record<string, string>) => {
    Object.assign(citationsState, { visible: true, content, type: typeof content });
    columns.value = getTableColumns();
  };

  const getTableColumns = () => {
    if (!citationsState.content) return [];
    const keys = Object.keys(citationsState.content);
    const columns: ColumnType[] = keys.map((key: string) => {
      return {
        title: key,
        dataIndex: key,
      };
    });
    return columns;
  };
  // 停止生成
  const stopGeneration = () => {
    if (abortController.value) {
      abortController.value.abort();
      abortController.value = null;
    }
    isLoading.value = false;
    if (messages.length && !messages[messages.length - 1].content) {
      messages[messages.length - 1].content = '已停止';
    }
    clearTimeout(typingInterval.value);
    typingInterval.value = null;
  };

  // 组件卸载时中止请求
  onUnmounted(() => {
    stopGeneration();
    clearTimeout(typingInterval.value);
    typingInterval.value = null;
  });

  onMounted(async () => {
    spinning.value = true;
    try {
      const data: IAgentItem =
        route.name === 'intelligent-detail'
          ? await agentVersionDetail(String(route.params.id))
          : await getAgentDetail(String(route.params.id));
      const { icon_url, name, deploy_id, config } = data;
      const { instruction, memory_config, knowledge_db, opener_prompt, suggested_questions, background_image } = config;
      Object.assign(basicState, { logo: icon_url, name, deploy_id });
      if (isDebug.value) {
        Object.assign(sendParams, {
          instruction: instruction.value || '',
          // 在调试中，知识库开关开了才传知识库id
          knowledge_db_ids: knowledge_db.is_enable
            ? knowledge_db.value
              ? knowledge_db.value.map((item) => item.id)
              : []
            : [],
          memory_vars: memory_config.value
            ? memory_config.value.map((item) => {
                return {
                  ...item,
                  current_value: '',
                };
              })
            : [],
        });
      }
      if (opener_prompt.is_enable) {
        basicState.prologue = opener_prompt.value;
      }
      if (background_image.is_enable) {
        basicState.background = background_image.value;
      }
      if (suggested_questions.is_enable) {
        basicState.suggestedQuestions = suggested_questions.value;
      }
    } catch {
      message.warn('应用异常，请联系管理员');
    }

    spinning.value = false;
  });
</script>

<template>
  <div v-if="spinning" class="w-100% h-100% flex items-center justify-center">
    <a-spin />
  </div>
  <div v-else class="preview-container">
    <div
      class="preview-content"
      :style="{ width: isDebug ? '100%' : '70vw', backgroundImage: `url(${basicState.background})` }"
    >
      <div class="content">
        <div class="header">
          <!-- <img :src="basicState.logo" alt="" /> -->
          <div class="img-content" :style="{ backgroundImage: `url(${basicState.logo})` }" />
          <div class="text-24px mt-10px">{{ basicState.name }}</div>
        </div>
        <div v-if="basicState.prologue || basicState.suggestedQuestions.length" class="prologue overflow-scroll">
          <div v-if="basicState.prologue" class="mb-10px leading-24px">{{ basicState.prologue }}</div>
          <div v-if="basicState.suggestedQuestions.length" class="flex flex-col items-start">
            <div
              v-for="(item, index) in basicState.suggestedQuestions"
              :key="index"
              class="question-item"
              @click="handleSend(item)"
            >
              <div class="question-item-msg">{{ item }}</div>
              <RightCircleOutlined />
            </div>
          </div>
        </div>
      </div>
      <div ref="containerRef" class="chat-container overflow-scroll">
        <div class="messages flex flex-col">
          <div
            v-for="(message, index) in messages"
            :key="index"
            class="message"
            ref="messageRef"
            :style="{ justifyContent: message.role === 'user' ? 'flex-end' : 'start' }"
          >
            <div v-if="message.content" :class="message.role" class="message-content">
              <div v-html="message.content.replace(/<citation>.*?<\/citation>/g, '')"></div>
              <div v-if="message.role === 'assistant' && message.citations && message.citations.length">
                <div class="ml-10px mb-10px text-#797979">{{ `切片（${message.citations.length}）` }}</div>
                <div class="citation flex">
                  <div
                    class="citation-item"
                    v-for="(item, index) in message.citations"
                    :key="item"
                    @click="handleClickCitation(item)"
                    @cancel="citationsState.visible = false"
                  >
                    {{ typeof item === 'string' ? item : `切片信息${index + 1}` }}
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="message-content"><a-spin /></div>
          </div>
        </div>
      </div>
      <div class="chat-box">
        <a-textarea
          v-model:value="inputMessage"
          class="custom-textarea"
          placeholder="请输入你的问题"
          :auto-size="{ minRows: 1, maxRows: 4 }"
          @change="handleChange"
          @press-enter.prevent="handleSend(inputMessage)"
        />

        <div class="flex flex-justify-end">
          <a-tooltip :arrow-point-at-center="true" :open="open || deployOpen">
            <template #title>{{ open ? '请输入您的问题' : deployOpen ? '模型未配置，请配置后再输入' : '' }}</template>
            <a-button
              v-if="isLoading"
              type="primary"
              :disabled="!basicState.deploy_id || !inputMessage || !inputMessage.trim()"
              shape="circle"
            >
              <StopOutlined @click="stopGeneration" />
            </a-button>
            <template v-else>
              <img
                v-if="!basicState.deploy_id || !inputMessage || !inputMessage.trim()"
                class="send-disabled-icon"
                :src="sendDisableBtn"
                alt=""
                :preview="false"
              />
              <img v-else class="send-icon" :src="sendBtn" alt="" :preview="false" @click="handleSend(inputMessage)" />
            </template>
          </a-tooltip>
        </div>
      </div>
    </div>
  </div>

  <a-modal v-model:open="citationsState.visible" width="50%" centered title="回答来源" :footer="false">
    <div v-if="citationsState.type === 'string'" class="citation-model overflow-scroll">
      {{ citationsState.content }}
    </div>
    <a-table v-else :data-source="[citationsState.content]" :columns="columns" :scroll="{ x: 'max-content' }">
      <template #bodyCell="{ text }">
        {{ String(text) }}
      </template>
    </a-table>
  </a-modal>
</template>

<style scoped lang="less">
  .preview-container {
    width: 100%;
    height: 100%; /* 高度自适应 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .preview-content {
    height: 100%; /* 高度自适应 */
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    padding: 20px;
    padding-bottom: 0;
    display: flex;
    flex-direction: column;
  }
  .content {
    padding-bottom: 15px;
  }
  .header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    // img {
    //   width: 96px;
    //   height: 96px;
    // }

    .img-content {
      width: 96px; /* 宽度自适应 */
      height: 96px; /* 高度自适应 */
      background-color: #fff;
      border-radius: 10px;
      // background-image: url('your-image.jpg');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
    }
  }
  .prologue {
    background: #fff;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 10px;
    max-height: 200px;
    .question-item {
      display: flex;
      cursor: pointer;
      justify-content: space-between;
      width: auto;
      padding: 10px;
      background-color: #f2f8ff;
      color: #1777ff;
      border-radius: 20px;
      margin-bottom: 5px;
      .question-item-msg {
        margin-right: 10px;
      }
    }
  }

  .chat-container {
    flex: 1;
    overflow-anchor: auto; /* 关键属性，让滚动条自动跟随新内容 */
    .messages {
      display: flex;
      flex-direction: column;
      .message {
        display: flex;
        margin-bottom: 10px;
        .message-content {
          padding: 10px;
          max-width: 80%;
          line-height: 20px;
          word-break: break-all;
          background-color: #fff;
          border: 1px solid #eee;
          border-radius: 10px;
        }
        .user {
          border-bottom-right-radius: 0;
        }
        .assistant {
          border-bottom-left-radius: 0;
        }
      }
    }
  }
  .chat-box {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    width: 100%;
    height: auto;
    padding: 0 10px;
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);

    :deep(.custom-textarea) {
      position: relative;
      box-sizing: border-box;
      height: auto;
      padding: 10px;
      font-family: PingFangSC, 'PingFang SC';
      font-size: 16px;
      font-weight: 400;
      background: rgb(255 255 255 / 20%);
      border-radius: 6px;
      overflow: hidden; /* 隐藏滚动条 */
      resize: none; /* 禁止用户调整大小 */

      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        display: none;
      }

      /* 占位符样式 */
      &::placeholder {
        color: #cccccc;
      }
    }
    .send-icon,
    .send-disabled-icon {
      width: 32px;
      height: 32px;
      cursor: pointer;
    }
    .send-disabled-icon {
      cursor: not-allowed;
    }
  }

  :deep(.ant-input) {
    border: none;
    &:focus {
      box-shadow: none !important;
    }
  }
  .citation-item {
    max-width: calc(50% - 10px);
    white-space: nowrap; /* 禁止换行 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis;
    cursor: pointer;
    background-color: #f2f8ff;
    color: #1777ff;
    border-radius: 20px;
    margin-bottom: 5px;
    padding: 10px;
    margin: 0 5px;
  }

  .citation-model {
    max-height: 400px;
    padding: 10px;
  }
</style>
